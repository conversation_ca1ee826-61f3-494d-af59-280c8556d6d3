/**
 * 检查视觉脚本节点文件中的错误
 */
const fs = require('fs');
const path = require('path');

/**
 * 检查单个文件的错误
 */
function checkFileErrors(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const errors = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const lineNum = index + 1;
      const trimmed = line.trim();
      
      // 检查未定义的类引用
      if (trimmed.includes('new ') && trimmed.includes('Manager')) {
        const match = trimmed.match(/new\s+(\w+Manager)/);
        if (match) {
          const className = match[1];
          // 检查该类是否在文件中定义
          if (!content.includes(`class ${className}`)) {
            errors.push({
              line: lineNum,
              type: 'UNDEFINED_CLASS',
              message: `未定义的类: ${className}`,
              code: trimmed
            });
          }
        }
      }
      
      // 检查未使用的导入
      if (trimmed.startsWith('import ') && trimmed.includes('NodeCategory')) {
        // 检查是否使用了NodeCategory
        if (!content.includes('NodeCategory.') && !content.includes('NodeCategory,')) {
          errors.push({
            line: lineNum,
            type: 'UNUSED_IMPORT',
            message: '未使用的导入: NodeCategory',
            code: trimmed
          });
        }
      }
      
      // 检查重复的类定义（精确匹配）
      if (trimmed.startsWith('class ') || trimmed.startsWith('export class ')) {
        const match = trimmed.match(/(?:export\s+)?class\s+(\w+)/);
        if (match) {
          const className = match[1];
          // 精确匹配类定义，避免误报
          const classRegex = new RegExp(`(?:export\\s+)?class\\s+${className}(?:\\s|{)`, 'g');
          const matches = content.match(classRegex);
          if (matches && matches.length > 1) {
            // 进一步检查是否真的是重复定义
            const lines = content.split('\n');
            const classLines = lines.filter(line => {
              const trimmedLine = line.trim();
              return trimmedLine.match(new RegExp(`(?:export\\s+)?class\\s+${className}(?:\\s|{)`));
            });

            if (classLines.length > 1) {
              errors.push({
                line: lineNum,
                type: 'DUPLICATE_CLASS',
                message: `重复的类定义: ${className}`,
                code: trimmed
              });
            }
          }
        }
      }
      
      // 检查语法错误模式
      if (trimmed.includes('export class') && !trimmed.includes('extends') && !trimmed.includes('{')) {
        errors.push({
          line: lineNum,
          type: 'SYNTAX_ERROR',
          message: '可能的语法错误: 类定义不完整',
          code: trimmed
        });
      }

      // 检查导入路径错误
      if (trimmed.startsWith('import ') && trimmed.includes('from ')) {
        // 检查相对路径是否正确
        if (trimmed.includes("from '../../../visualscript/")) {
          // 这是正确的路径格式
        } else if (trimmed.includes("from '../../visualscript/")) {
          errors.push({
            line: lineNum,
            type: 'IMPORT_PATH_ERROR',
            message: '导入路径可能错误: 应该是 ../../../visualscript/',
            code: trimmed
          });
        }

        // 检查是否导入了不存在的模块
        if (trimmed.includes("from 'three'") && !content.includes('Vector3') && !content.includes('Object3D')) {
          errors.push({
            line: lineNum,
            type: 'UNUSED_IMPORT',
            message: '可能未使用的three.js导入',
            code: trimmed
          });
        }
      }

      // 检查类型错误
      if (trimmed.includes(': ') && trimmed.includes('=')) {
        // 检查类型注解
        if (trimmed.includes(': any') && !trimmed.includes('Map<') && !trimmed.includes('Record<')) {
          errors.push({
            line: lineNum,
            type: 'TYPE_WARNING',
            message: '使用了any类型，建议使用更具体的类型',
            code: trimmed
          });
        }
      }
    });
    
    return errors;
  } catch (error) {
    return [{
      line: 0,
      type: 'FILE_ERROR',
      message: `文件读取错误: ${error.message}`,
      code: ''
    }];
  }
}

/**
 * 递归检查目录
 */
function checkDirectory(dirPath) {
  const results = [];
  
  if (!fs.existsSync(dirPath)) {
    return results;
  }
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      results.push(...checkDirectory(itemPath));
    } else if (item.endsWith('.ts') && !item.includes('test') && !item.includes('spec')) {
      const errors = checkFileErrors(itemPath);
      if (errors.length > 0) {
        results.push({
          file: itemPath,
          errors: errors
        });
      }
    }
  }
  
  return results;
}

/**
 * 主函数
 */
function main() {
  console.log('=== 检查视觉脚本节点文件错误 ===\n');
  
  const nodePaths = [
    'engine/src/visual-script/nodes',
    'engine/src/visual-script/registry'
  ];
  
  let totalErrors = 0;
  let totalFiles = 0;
  
  nodePaths.forEach(nodePath => {
    if (fs.existsSync(nodePath)) {
      console.log(`检查路径: ${nodePath}`);
      const results = checkDirectory(nodePath);
      
      results.forEach(result => {
        totalFiles++;
        console.log(`\n📁 文件: ${result.file}`);
        
        result.errors.forEach(error => {
          totalErrors++;
          console.log(`  ❌ 第${error.line}行 [${error.type}]: ${error.message}`);
          if (error.code) {
            console.log(`     代码: ${error.code}`);
          }
        });
      });
      
      console.log(`   检查完成，发现 ${results.length} 个有错误的文件`);
    } else {
      console.log(`路径不存在: ${nodePath}`);
    }
  });
  
  console.log(`\n=== 检查总结 ===`);
  console.log(`总共检查文件: ${totalFiles}`);
  console.log(`发现错误数量: ${totalErrors}`);
  
  if (totalErrors === 0) {
    console.log('🎉 没有发现错误！');
  } else {
    console.log('⚠️ 发现错误，需要修复');
  }
}

// 运行检查
main();
