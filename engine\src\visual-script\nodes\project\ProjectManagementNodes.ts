/**
 * 项目管理节点
 * 提供项目创建、加载、保存、版本控制、协作等功能
 */

import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';

/**
 * 项目配置接口
 */
export interface ProjectConfig {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  createdAt: Date;
  modifiedAt: Date;
  tags: string[];
  metadata: Record<string, any>;
}

/**
 * 项目权限接口
 */
export interface ProjectPermission {
  userId: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  permissions: string[];
  grantedAt: Date;
  grantedBy: string;
}

/**
 * 项目版本接口
 */
export interface ProjectVersion {
  version: string;
  description: string;
  author: string;
  createdAt: Date;
  changes: string[];
  data: any;
}

/**
 * 项目管理节点基类
 */
export abstract class ProjectManagementNode extends VisualScriptNode {
  constructor(nodeType: string, name: string, id?: string) {
    super(nodeType, name, id);
  }

  /**
   * 获取项目管理服务
   */
  protected getProjectService(): any {
    // 这里应该返回实际的项目管理服务实例
    return {
      createProject: async (config: ProjectConfig) => config,
      loadProject: async (projectId: string) => null,
      saveProject: async (projectId: string, data: any) => true,
      deleteProject: async (projectId: string) => true,
      getProjectVersions: async (projectId: string) => [],
      createVersion: async (projectId: string, version: ProjectVersion) => version,
      getProjectPermissions: async (projectId: string) => [],
      setProjectPermission: async (projectId: string, permission: ProjectPermission) => true,
      backupProject: async (projectId: string) => true,
      getProjectAnalytics: async (projectId: string) => {},
      exportProject: async (projectId: string, format: string) => null
    };
  }
}

/**
 * 创建项目节点
 */
export class CreateProjectNode extends ProjectManagementNode {
  static readonly TYPE = 'CreateProject';
  static readonly NAME = '创建项目';
  static readonly DESCRIPTION = '创建新的项目';

  constructor(nodeType: string = CreateProjectNode.TYPE, name: string = CreateProjectNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectName', 'string', '项目名称', '新项目');
    this.addInput('description', 'string', '项目描述', '');
    this.addInput('author', 'string', '作者', 'System');
    this.addInput('tags', 'array', '标签', []);
    this.addInput('metadata', 'object', '元数据', {});

    // 输出端口
    this.addOutput('projectId', 'string', '项目ID');
    this.addOutput('project', 'object', '项目对象');
    this.addOutput('success', 'boolean', '创建成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectName = this.getInputValue('projectName', context) || '新项目';
      const description = this.getInputValue('description', context) || '';
      const author = this.getInputValue('author', context) || 'System';
      const tags = this.getInputValue('tags', context) || [];
      const metadata = this.getInputValue('metadata', context) || {};

      const projectConfig: ProjectConfig = {
        id: this.generateProjectId(),
        name: projectName,
        description,
        version: '1.0.0',
        author,
        createdAt: new Date(),
        modifiedAt: new Date(),
        tags,
        metadata
      };

      const projectService = this.getProjectService();
      const project = await projectService.createProject(projectConfig);

      this.setOutputValue('projectId', project.id, context);
      this.setOutputValue('project', project, context);
      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('projectId', '', context);
      this.setOutputValue('project', null, context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '创建项目失败', context);
    }
  }

  private generateProjectId(): string {
    return `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 加载项目节点
 */
export class LoadProjectNode extends ProjectManagementNode {
  static readonly TYPE = 'LoadProject';
  static readonly NAME = '加载项目';
  static readonly DESCRIPTION = '加载现有项目';

  constructor(nodeType: string = LoadProjectNode.TYPE, name: string = LoadProjectNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('version', 'string', '版本号', 'latest');

    // 输出端口
    this.addOutput('project', 'object', '项目对象');
    this.addOutput('success', 'boolean', '加载成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectId = this.getInputValue('projectId', context);
      const version = this.getInputValue('version', context) || 'latest';

      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      const projectService = this.getProjectService();
      const project = await projectService.loadProject(projectId, version);

      if (!project) {
        throw new Error('项目不存在或无法访问');
      }

      this.setOutputValue('project', project, context);
      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('project', null, context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '加载项目失败', context);
    }
  }
}

/**
 * 保存项目节点
 */
export class SaveProjectNode extends ProjectManagementNode {
  static readonly TYPE = 'SaveProject';
  static readonly NAME = '保存项目';
  static readonly DESCRIPTION = '保存项目数据';

  constructor(nodeType: string = SaveProjectNode.TYPE, name: string = SaveProjectNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('projectData', 'object', '项目数据', {});
    this.addInput('autoVersion', 'boolean', '自动版本控制', true);
    this.addInput('versionDescription', 'string', '版本描述', '');

    // 输出端口
    this.addOutput('success', 'boolean', '保存成功');
    this.addOutput('version', 'string', '版本号');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectId = this.getInputValue('projectId', context);
      const projectData = this.getInputValue('projectData', context) || {};
      const autoVersion = this.getInputValue('autoVersion', context) !== false;
      const versionDescription = this.getInputValue('versionDescription', context) || '';

      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      const projectService = this.getProjectService();
      
      // 保存项目数据
      const saveResult = await projectService.saveProject(projectId, projectData);
      
      let version = '';
      if (autoVersion && saveResult) {
        // 创建新版本
        const versionData: ProjectVersion = {
          version: this.generateVersion(),
          description: versionDescription || '自动保存',
          author: 'System',
          createdAt: new Date(),
          changes: ['项目数据更新'],
          data: projectData
        };
        
        const versionResult = await projectService.createVersion(projectId, versionData);
        version = versionResult.version;
      }

      this.setOutputValue('success', saveResult, context);
      this.setOutputValue('version', version, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('success', false, context);
      this.setOutputValue('version', '', context);
      this.setOutputValue('error', error instanceof Error ? error.message : '保存项目失败', context);
    }
  }

  private generateVersion(): string {
    const now = new Date();
    return `v${now.getFullYear()}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getDate().toString().padStart(2, '0')}.${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`;
  }
}

/**
 * 项目版本节点
 */
export class ProjectVersionNode extends ProjectManagementNode {
  static readonly TYPE = 'ProjectVersion';
  static readonly NAME = '项目版本';
  static readonly DESCRIPTION = '管理项目版本';

  constructor(nodeType: string = ProjectVersionNode.TYPE, name: string = ProjectVersionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('action', 'string', '操作类型', 'list'); // list, create, get, delete
    this.addInput('version', 'string', '版本号', '');
    this.addInput('description', 'string', '版本描述', '');
    this.addInput('data', 'object', '版本数据', {});

    // 输出端口
    this.addOutput('versions', 'array', '版本列表');
    this.addOutput('versionData', 'object', '版本数据');
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectId = this.getInputValue('projectId', context);
      const action = this.getInputValue('action', context) || 'list';

      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      const projectService = this.getProjectService();
      let result: any = null;

      switch (action) {
        case 'list':
          result = await projectService.getProjectVersions(projectId);
          this.setOutputValue('versions', result, context);
          break;

        case 'create':
          const version = this.getInputValue('version', context) || this.generateVersion();
          const description = this.getInputValue('description', context) || '';
          const data = this.getInputValue('data', context) || {};

          const versionData: ProjectVersion = {
            version,
            description,
            author: 'System',
            createdAt: new Date(),
            changes: ['手动创建版本'],
            data
          };

          result = await projectService.createVersion(projectId, versionData);
          this.setOutputValue('versionData', result, context);
          break;

        case 'get':
          const targetVersion = this.getInputValue('version', context);
          if (!targetVersion) {
            throw new Error('版本号不能为空');
          }
          result = await projectService.getVersion(projectId, targetVersion);
          this.setOutputValue('versionData', result, context);
          break;

        default:
          throw new Error(`不支持的操作类型: ${action}`);
      }

      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('versions', [], context);
      this.setOutputValue('versionData', null, context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '版本操作失败', context);
    }
  }

  private generateVersion(): string {
    const now = new Date();
    return `v${now.getFullYear()}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getDate().toString().padStart(2, '0')}.${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`;
  }
}

/**
 * 项目协作节点
 */
export class ProjectCollaborationNode extends ProjectManagementNode {
  static readonly TYPE = 'ProjectCollaboration';
  static readonly NAME = '项目协作';
  static readonly DESCRIPTION = '管理项目协作功能';

  constructor(nodeType: string = ProjectCollaborationNode.TYPE, name: string = ProjectCollaborationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('action', 'string', '操作类型', 'getCollaborators'); // getCollaborators, invite, remove, updateRole
    this.addInput('userId', 'string', '用户ID', '');
    this.addInput('email', 'string', '邮箱地址', '');
    this.addInput('role', 'string', '角色', 'viewer'); // owner, admin, editor, viewer
    this.addInput('message', 'string', '邀请消息', '');

    // 输出端口
    this.addOutput('collaborators', 'array', '协作者列表');
    this.addOutput('invitation', 'object', '邀请信息');
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectId = this.getInputValue('projectId', context);
      const action = this.getInputValue('action', context) || 'getCollaborators';

      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      const collaborationService = this.getCollaborationService();
      let result: any = null;

      switch (action) {
        case 'getCollaborators':
          result = await collaborationService.getCollaborators(projectId);
          this.setOutputValue('collaborators', result, context);
          break;

        case 'invite':
          const email = this.getInputValue('email', context);
          const role = this.getInputValue('role', context) || 'viewer';
          const message = this.getInputValue('message', context) || '';

          if (!email) {
            throw new Error('邮箱地址不能为空');
          }

          result = await collaborationService.inviteCollaborator(projectId, email, role, message);
          this.setOutputValue('invitation', result, context);
          break;

        case 'remove':
          const userId = this.getInputValue('userId', context);
          if (!userId) {
            throw new Error('用户ID不能为空');
          }

          result = await collaborationService.removeCollaborator(projectId, userId);
          break;

        case 'updateRole':
          const targetUserId = this.getInputValue('userId', context);
          const newRole = this.getInputValue('role', context);

          if (!targetUserId || !newRole) {
            throw new Error('用户ID和角色不能为空');
          }

          result = await collaborationService.updateCollaboratorRole(projectId, targetUserId, newRole);
          break;

        default:
          throw new Error(`不支持的操作类型: ${action}`);
      }

      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('collaborators', [], context);
      this.setOutputValue('invitation', null, context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '协作操作失败', context);
    }
  }

  private getCollaborationService(): any {
    return {
      getCollaborators: async (projectId: string) => [],
      inviteCollaborator: async (projectId: string, email: string, role: string, message: string) => ({ inviteId: 'invite_123', email, role }),
      removeCollaborator: async (projectId: string, userId: string) => true,
      updateCollaboratorRole: async (projectId: string, userId: string, role: string) => true
    };
  }
}

/**
 * 项目权限节点
 */
export class ProjectPermissionNode extends ProjectManagementNode {
  static readonly TYPE = 'ProjectPermission';
  static readonly NAME = '项目权限';
  static readonly DESCRIPTION = '管理项目权限';

  constructor(nodeType: string = ProjectPermissionNode.TYPE, name: string = ProjectPermissionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('userId', 'string', '用户ID', '');
    this.addInput('action', 'string', '操作类型', 'check'); // check, grant, revoke, list
    this.addInput('permission', 'string', '权限名称', '');
    this.addInput('role', 'string', '角色', '');

    // 输出端口
    this.addOutput('hasPermission', 'boolean', '是否有权限');
    this.addOutput('permissions', 'array', '权限列表');
    this.addOutput('userRole', 'string', '用户角色');
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectId = this.getInputValue('projectId', context);
      const userId = this.getInputValue('userId', context);
      const action = this.getInputValue('action', context) || 'check';

      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      const permissionService = this.getPermissionService();
      let result: any = null;

      switch (action) {
        case 'check':
          const permission = this.getInputValue('permission', context);
          if (!userId || !permission) {
            throw new Error('用户ID和权限名称不能为空');
          }

          result = await permissionService.checkPermission(projectId, userId, permission);
          this.setOutputValue('hasPermission', result, context);
          break;

        case 'list':
          if (!userId) {
            throw new Error('用户ID不能为空');
          }

          result = await permissionService.getUserPermissions(projectId, userId);
          this.setOutputValue('permissions', result.permissions, context);
          this.setOutputValue('userRole', result.role, context);
          break;

        case 'grant':
          const grantPermission = this.getInputValue('permission', context);
          if (!userId || !grantPermission) {
            throw new Error('用户ID和权限名称不能为空');
          }

          result = await permissionService.grantPermission(projectId, userId, grantPermission);
          break;

        case 'revoke':
          const revokePermission = this.getInputValue('permission', context);
          if (!userId || !revokePermission) {
            throw new Error('用户ID和权限名称不能为空');
          }

          result = await permissionService.revokePermission(projectId, userId, revokePermission);
          break;

        default:
          throw new Error(`不支持的操作类型: ${action}`);
      }

      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('hasPermission', false, context);
      this.setOutputValue('permissions', [], context);
      this.setOutputValue('userRole', '', context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '权限操作失败', context);
    }
  }

  private getPermissionService(): any {
    return {
      checkPermission: async (projectId: string, userId: string, permission: string) => true,
      getUserPermissions: async (projectId: string, userId: string) => ({ role: 'editor', permissions: ['read', 'write'] }),
      grantPermission: async (projectId: string, userId: string, permission: string) => true,
      revokePermission: async (projectId: string, userId: string, permission: string) => true
    };
  }
}

/**
 * 项目备份节点
 */
export class ProjectBackupNode extends ProjectManagementNode {
  static readonly TYPE = 'ProjectBackup';
  static readonly NAME = '项目备份';
  static readonly DESCRIPTION = '管理项目备份';

  constructor(nodeType: string = ProjectBackupNode.TYPE, name: string = ProjectBackupNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('action', 'string', '操作类型', 'create'); // create, restore, list, delete
    this.addInput('backupId', 'string', '备份ID', '');
    this.addInput('description', 'string', '备份描述', '');
    this.addInput('includeAssets', 'boolean', '包含资源', true);

    // 输出端口
    this.addOutput('backupId', 'string', '备份ID');
    this.addOutput('backups', 'array', '备份列表');
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectId = this.getInputValue('projectId', context);
      const action = this.getInputValue('action', context) || 'create';

      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      const backupService = this.getBackupService();
      let result: any = null;

      switch (action) {
        case 'create':
          const description = this.getInputValue('description', context) || '';
          const includeAssets = this.getInputValue('includeAssets', context) !== false;

          result = await backupService.createBackup(projectId, {
            description,
            includeAssets,
            createdAt: new Date()
          });

          this.setOutputValue('backupId', result.backupId, context);
          break;

        case 'list':
          result = await backupService.listBackups(projectId);
          this.setOutputValue('backups', result, context);
          break;

        case 'restore':
          const backupId = this.getInputValue('backupId', context);
          if (!backupId) {
            throw new Error('备份ID不能为空');
          }

          result = await backupService.restoreBackup(projectId, backupId);
          break;

        case 'delete':
          const deleteBackupId = this.getInputValue('backupId', context);
          if (!deleteBackupId) {
            throw new Error('备份ID不能为空');
          }

          result = await backupService.deleteBackup(projectId, deleteBackupId);
          break;

        default:
          throw new Error(`不支持的操作类型: ${action}`);
      }

      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('backupId', '', context);
      this.setOutputValue('backups', [], context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '备份操作失败', context);
    }
  }

  private getBackupService(): any {
    return {
      createBackup: async (projectId: string, options: any) => ({ backupId: `backup_${Date.now()}`, ...options }),
      listBackups: async (projectId: string) => [],
      restoreBackup: async (projectId: string, backupId: string) => true,
      deleteBackup: async (projectId: string, backupId: string) => true
    };
  }
}

/**
 * 项目分析节点
 */
export class ProjectAnalyticsNode extends ProjectManagementNode {
  static readonly TYPE = 'ProjectAnalytics';
  static readonly NAME = '项目分析';
  static readonly DESCRIPTION = '获取项目分析数据';

  constructor(nodeType: string = ProjectAnalyticsNode.TYPE, name: string = ProjectAnalyticsNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('metricType', 'string', '指标类型', 'overview'); // overview, performance, usage, collaboration
    this.addInput('timeRange', 'string', '时间范围', '7d'); // 1d, 7d, 30d, 90d
    this.addInput('includeDetails', 'boolean', '包含详细信息', false);

    // 输出端口
    this.addOutput('analytics', 'object', '分析数据');
    this.addOutput('summary', 'object', '摘要信息');
    this.addOutput('success', 'boolean', '获取成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectId = this.getInputValue('projectId', context);
      const metricType = this.getInputValue('metricType', context) || 'overview';
      const timeRange = this.getInputValue('timeRange', context) || '7d';
      const includeDetails = this.getInputValue('includeDetails', context) === true;

      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      const analyticsService = this.getAnalyticsService();
      const analytics = await analyticsService.getProjectAnalytics(projectId, {
        metricType,
        timeRange,
        includeDetails
      });

      const summary = this.generateSummary(analytics, metricType);

      this.setOutputValue('analytics', analytics, context);
      this.setOutputValue('summary', summary, context);
      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('analytics', {}, context);
      this.setOutputValue('summary', {}, context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '获取分析数据失败', context);
    }
  }

  private generateSummary(analytics: any, metricType: string): any {
    // 根据指标类型生成摘要
    switch (metricType) {
      case 'overview':
        return {
          totalViews: analytics.views || 0,
          totalEdits: analytics.edits || 0,
          activeUsers: analytics.activeUsers || 0,
          lastActivity: analytics.lastActivity || null
        };
      case 'performance':
        return {
          averageLoadTime: analytics.averageLoadTime || 0,
          errorRate: analytics.errorRate || 0,
          uptime: analytics.uptime || 100
        };
      case 'usage':
        return {
          dailyActiveUsers: analytics.dailyActiveUsers || 0,
          sessionDuration: analytics.sessionDuration || 0,
          featureUsage: analytics.featureUsage || {}
        };
      case 'collaboration':
        return {
          totalCollaborators: analytics.totalCollaborators || 0,
          activeCollaborators: analytics.activeCollaborators || 0,
          commentsCount: analytics.commentsCount || 0
        };
      default:
        return {};
    }
  }

  private getAnalyticsService(): any {
    return {
      getProjectAnalytics: async (projectId: string, options: any) => ({
        views: 100,
        edits: 50,
        activeUsers: 5,
        lastActivity: new Date(),
        averageLoadTime: 1.2,
        errorRate: 0.01,
        uptime: 99.9,
        dailyActiveUsers: 3,
        sessionDuration: 1800,
        featureUsage: { editor: 80, preview: 60, export: 20 },
        totalCollaborators: 5,
        activeCollaborators: 3,
        commentsCount: 15
      })
    };
  }
}

/**
 * 项目模板节点
 */
export class ProjectTemplateNode extends ProjectManagementNode {
  static readonly TYPE = 'ProjectTemplate';
  static readonly NAME = '项目模板';
  static readonly DESCRIPTION = '管理项目模板';

  constructor(nodeType: string = ProjectTemplateNode.TYPE, name: string = ProjectTemplateNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '操作类型', 'list'); // list, create, apply, delete
    this.addInput('templateId', 'string', '模板ID', '');
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('templateName', 'string', '模板名称', '');
    this.addInput('templateData', 'object', '模板数据', {});
    this.addInput('category', 'string', '模板分类', 'general');

    // 输出端口
    this.addOutput('templates', 'array', '模板列表');
    this.addOutput('template', 'object', '模板对象');
    this.addOutput('newProjectId', 'string', '新项目ID');
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const action = this.getInputValue('action', context) || 'list';
      const templateService = this.getTemplateService();
      let result: any = null;

      switch (action) {
        case 'list':
          const category = this.getInputValue('category', context);
          result = await templateService.listTemplates(category);
          this.setOutputValue('templates', result, context);
          break;

        case 'create':
          const projectId = this.getInputValue('projectId', context);
          const templateName = this.getInputValue('templateName', context);
          const templateData = this.getInputValue('templateData', context) || {};

          if (!projectId || !templateName) {
            throw new Error('项目ID和模板名称不能为空');
          }

          result = await templateService.createTemplate(projectId, templateName, templateData);
          this.setOutputValue('template', result, context);
          break;

        case 'apply':
          const templateId = this.getInputValue('templateId', context);
          const targetProjectName = this.getInputValue('templateName', context) || '基于模板的新项目';

          if (!templateId) {
            throw new Error('模板ID不能为空');
          }

          result = await templateService.applyTemplate(templateId, targetProjectName);
          this.setOutputValue('newProjectId', result.projectId, context);
          break;

        case 'delete':
          const deleteTemplateId = this.getInputValue('templateId', context);
          if (!deleteTemplateId) {
            throw new Error('模板ID不能为空');
          }

          result = await templateService.deleteTemplate(deleteTemplateId);
          break;

        default:
          throw new Error(`不支持的操作类型: ${action}`);
      }

      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('templates', [], context);
      this.setOutputValue('template', null, context);
      this.setOutputValue('newProjectId', '', context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '模板操作失败', context);
    }
  }

  private getTemplateService(): any {
    return {
      listTemplates: async (category?: string) => [
        { id: 'template_1', name: '基础3D场景', category: 'general' },
        { id: 'template_2', name: 'VR应用模板', category: 'vr' }
      ],
      createTemplate: async (projectId: string, name: string, data: any) => ({
        id: `template_${Date.now()}`,
        name,
        projectId,
        data
      }),
      applyTemplate: async (templateId: string, projectName: string) => ({
        projectId: `project_${Date.now()}`,
        templateId,
        name: projectName
      }),
      deleteTemplate: async (templateId: string) => true
    };
  }
}

/**
 * 项目导出节点
 */
export class ProjectExportNode extends ProjectManagementNode {
  static readonly TYPE = 'ProjectExport';
  static readonly NAME = '项目导出';
  static readonly DESCRIPTION = '导出项目数据';

  constructor(nodeType: string = ProjectExportNode.TYPE, name: string = ProjectExportNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('projectId', 'string', '项目ID', '');
    this.addInput('format', 'string', '导出格式', 'json'); // json, zip, gltf, fbx, obj
    this.addInput('includeAssets', 'boolean', '包含资源', true);
    this.addInput('includeMetadata', 'boolean', '包含元数据', true);
    this.addInput('compressionLevel', 'number', '压缩级别', 5); // 0-9
    this.addInput('exportPath', 'string', '导出路径', '');

    // 输出端口
    this.addOutput('exportUrl', 'string', '导出文件URL');
    this.addOutput('exportData', 'object', '导出数据');
    this.addOutput('fileSize', 'number', '文件大小');
    this.addOutput('success', 'boolean', '导出成功');
    this.addOutput('error', 'string', '错误信息');
  }

  async execute(context: any): Promise<void> {
    try {
      const projectId = this.getInputValue('projectId', context);
      const format = this.getInputValue('format', context) || 'json';
      const includeAssets = this.getInputValue('includeAssets', context) !== false;
      const includeMetadata = this.getInputValue('includeMetadata', context) !== false;
      const compressionLevel = this.getInputValue('compressionLevel', context) || 5;
      const exportPath = this.getInputValue('exportPath', context) || '';

      if (!projectId) {
        throw new Error('项目ID不能为空');
      }

      const exportService = this.getExportService();
      const exportOptions = {
        format,
        includeAssets,
        includeMetadata,
        compressionLevel,
        exportPath
      };

      const exportResult = await exportService.exportProject(projectId, exportOptions);

      this.setOutputValue('exportUrl', exportResult.url, context);
      this.setOutputValue('exportData', exportResult.data, context);
      this.setOutputValue('fileSize', exportResult.fileSize, context);
      this.setOutputValue('success', true, context);
      this.setOutputValue('error', '', context);

    } catch (error) {
      this.setOutputValue('exportUrl', '', context);
      this.setOutputValue('exportData', null, context);
      this.setOutputValue('fileSize', 0, context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '导出项目失败', context);
    }
  }

  private getExportService(): any {
    return {
      exportProject: async (projectId: string, options: any) => {
        // 模拟导出过程
        const timestamp = Date.now();
        const filename = `project_${projectId}_${timestamp}.${options.format}`;

        return {
          url: `/exports/${filename}`,
          data: options.format === 'json' ? { projectId, exportedAt: new Date() } : null,
          fileSize: Math.floor(Math.random() * 10000000) + 1000000, // 1-10MB
          format: options.format,
          exportedAt: new Date()
        };
      }
    };
  }
}

// 导出所有项目管理节点
export const PROJECT_MANAGEMENT_NODES = [
  CreateProjectNode,
  LoadProjectNode,
  SaveProjectNode,
  ProjectVersionNode,
  ProjectCollaborationNode,
  ProjectPermissionNode,
  ProjectBackupNode,
  ProjectAnalyticsNode,
  ProjectTemplateNode,
  ProjectExportNode
];
