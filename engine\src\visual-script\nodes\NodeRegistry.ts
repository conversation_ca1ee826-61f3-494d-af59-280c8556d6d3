/**
 * 视觉脚本节点注册表
 * 批次1.7和批次2.1节点注册
 * 注册所有新开发的动画系统增强节点和用户服务节点
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';

// 导入批次1.7动画系统增强节点
import { 
  AnimationBlendTreeNode,
  AnimationStateMachineNode,
  IKSystemNode,
  AnimationRetargetingNode,
  AnimationCompressionNode,
  AnimationOptimizationNode
} from './animation/AnimationNodes';

import {
  AnimationBakingNode,
  AnimationExportNode,
  AnimationImportNode,
  AnimationValidationNode
} from './animation/AnimationToolNodes';

// 导入批次2.1用户服务节点
import {
  UserAuthenticationNode,
  UserRegistrationNode
} from './user/UserServiceNodes';

import {
  UserProfileNode
} from './user/UserServiceNodes';

import {
  UserPermissionNode,
  UserRoleNode
} from './user/UserServiceNodes2';

import {
  UserSessionNode,
  UserPreferencesNode
} from './user/UserServiceNodes3';

import {
  UserActivityNode,
  UserAnalyticsNode
} from './user/UserServiceNodes4';

import {
  UserNotificationNode,
  UserGroupNode,
  UserSyncNode
} from './user/UserServiceNodes5';

/**
 * 节点注册表类
 */
export class NodeRegistry {
  private static instance: NodeRegistry;
  private nodeTypes: Map<string, typeof VisualScriptNode> = new Map();
  private nodeCategories: Map<string, string[]> = new Map();

  private constructor() {
    this.registerNodes();
  }

  public static getInstance(): NodeRegistry {
    if (!NodeRegistry.instance) {
      NodeRegistry.instance = new NodeRegistry();
    }
    return NodeRegistry.instance;
  }

  /**
   * 注册所有节点
   */
  private registerNodes(): void {
    // 注册批次1.7动画系统增强节点
    this.registerAnimationNodes();
    
    // 注册批次2.1用户服务节点
    this.registerUserServiceNodes();
  }

  /**
   * 注册动画系统增强节点
   */
  private registerAnimationNodes(): void {
    const animationNodes = [
      AnimationBlendTreeNode,
      AnimationStateMachineNode,
      IKSystemNode,
      AnimationRetargetingNode,
      AnimationCompressionNode,
      AnimationOptimizationNode,
      AnimationBakingNode,
      AnimationExportNode,
      AnimationImportNode,
      AnimationValidationNode
    ];

    const animationCategory = 'Animation/Advanced';
    this.nodeCategories.set(animationCategory, []);

    for (const NodeClass of animationNodes) {
      this.nodeTypes.set(NodeClass.TYPE, NodeClass);
      this.nodeCategories.get(animationCategory)?.push(NodeClass.TYPE);
    }

    console.log(`已注册 ${animationNodes.length} 个动画系统增强节点`);
  }

  /**
   * 注册用户服务节点
   */
  private registerUserServiceNodes(): void {
    const userServiceNodes = [
      UserAuthenticationNode,
      UserRegistrationNode,
      UserProfileNode,
      UserPermissionNode,
      UserRoleNode,
      UserSessionNode,
      UserPreferencesNode,
      UserActivityNode,
      UserAnalyticsNode,
      UserNotificationNode,
      UserGroupNode,
      UserSyncNode
    ];

    const userServiceCategory = 'User/Services';
    this.nodeCategories.set(userServiceCategory, []);

    for (const NodeClass of userServiceNodes) {
      this.nodeTypes.set(NodeClass.TYPE, NodeClass);
      this.nodeCategories.get(userServiceCategory)?.push(NodeClass.TYPE);
    }

    console.log(`已注册 ${userServiceNodes.length} 个用户服务节点`);
  }

  /**
   * 创建节点实例
   */
  public createNode(nodeType: string, id?: string): VisualScriptNode | null {
    const NodeClass = this.nodeTypes.get(nodeType);
    if (NodeClass) {
      return new NodeClass(nodeType, NodeClass.NAME, id);
    }
    return null;
  }

  /**
   * 获取所有注册的节点类型
   */
  public getRegisteredNodeTypes(): string[] {
    return Array.from(this.nodeTypes.keys());
  }

  /**
   * 获取节点分类
   */
  public getNodeCategories(): Map<string, string[]> {
    return new Map(this.nodeCategories);
  }

  /**
   * 获取指定分类的节点
   */
  public getNodesByCategory(category: string): string[] {
    return this.nodeCategories.get(category) || [];
  }

  /**
   * 检查节点类型是否已注册
   */
  public isNodeTypeRegistered(nodeType: string): boolean {
    return this.nodeTypes.has(nodeType);
  }

  /**
   * 获取节点信息
   */
  public getNodeInfo(nodeType: string): any {
    const NodeClass = this.nodeTypes.get(nodeType);
    if (NodeClass) {
      return {
        type: NodeClass.TYPE,
        name: NodeClass.NAME,
        description: NodeClass.DESCRIPTION || '无描述'
      };
    }
    return null;
  }

  /**
   * 获取所有节点信息
   */
  public getAllNodeInfo(): any[] {
    const nodeInfos: any[] = [];
    
    for (const [nodeType, NodeClass] of this.nodeTypes) {
      nodeInfos.push({
        type: NodeClass.TYPE,
        name: NodeClass.NAME,
        description: NodeClass.DESCRIPTION || '无描述'
      });
    }
    
    return nodeInfos;
  }

  /**
   * 获取节点统计信息
   */
  public getNodeStatistics(): any {
    const totalNodes = this.nodeTypes.size;
    const categories = this.nodeCategories.size;
    const categoryStats: any = {};

    for (const [category, nodes] of this.nodeCategories) {
      categoryStats[category] = nodes.length;
    }

    return {
      totalNodes,
      categories,
      categoryStats,
      registeredAt: new Date().toISOString()
    };
  }
}

/**
 * 导出单例实例
 */
export const nodeRegistry = NodeRegistry.getInstance();

/**
 * 便捷函数：创建节点
 */
export function createNode(nodeType: string, id?: string): VisualScriptNode | null {
  return nodeRegistry.createNode(nodeType, id);
}

/**
 * 便捷函数：获取所有节点类型
 */
export function getRegisteredNodeTypes(): string[] {
  return nodeRegistry.getRegisteredNodeTypes();
}

/**
 * 便捷函数：获取节点分类
 */
export function getNodeCategories(): Map<string, string[]> {
  return nodeRegistry.getNodeCategories();
}

/**
 * 便捷函数：获取节点信息
 */
export function getNodeInfo(nodeType: string): any {
  return nodeRegistry.getNodeInfo(nodeType);
}

/**
 * 便捷函数：获取所有节点信息
 */
export function getAllNodeInfo(): any[] {
  return nodeRegistry.getAllNodeInfo();
}

/**
 * 便捷函数：获取统计信息
 */
export function getNodeStatistics(): any {
  return nodeRegistry.getNodeStatistics();
}

// 初始化节点注册表
console.log('DL引擎视觉脚本节点注册表已初始化');
console.log('批次1.7动画系统增强节点：10个');
console.log('批次2.1用户服务节点：12个');
console.log('总计新增节点：22个');

const stats = getNodeStatistics();
console.log('节点统计信息：', stats);
