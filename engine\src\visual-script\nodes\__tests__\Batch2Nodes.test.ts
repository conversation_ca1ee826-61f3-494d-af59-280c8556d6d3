/**
 * 第二批次节点测试
 * 批次2.1和2.2节点功能测试
 */
import { describe, test, expect, beforeEach } from '@jest/globals';

// 通知服务节点
import {
  EmailNotificationNode,
  PushNotificationNode,
  SMSNotificationNode,
  InAppNotificationNode,
  NotificationTemplateNode,
  NotificationScheduleNode,
  NotificationAnalyticsNode,
  NotificationPreferencesNode
} from '../notification';

// 监控服务节点
import {
  SystemMonitoringNode,
  PerformanceMonitoringNode,
  ErrorTrackingNode,
  LogAnalysisNode,
  AlertSystemNode
} from '../monitoring';

// 后处理效果节点
import {
  BloomEffectNode,
  BlurEffectNode,
  ColorGradingNode,
  ToneMappingNode,
  SSAONode,
  SSRNode,
  MotionBlurNode,
  DepthOfFieldNode,
  FilmGrainNode,
  VignetteNode,
  ChromaticAberrationNode,
  LensDistortionNode,
  AntiAliasingNode,
  HDRProcessingNode,
  CustomPostProcessNode
} from '../postprocessing';

describe('批次2.1 通知服务节点测试', () => {
  describe('EmailNotificationNode', () => {
    let node: EmailNotificationNode;

    beforeEach(() => {
      node = new EmailNotificationNode();
    });

    test('应该正确创建邮件通知节点', () => {
      expect(node).toBeDefined();
      expect(node.nodeType).toBe('EmailNotification');
      expect(node.name).toBe('邮件通知');
    });

    test('应该能够发送邮件通知', () => {
      const result = node.execute({
        trigger: true,
        to: '<EMAIL>',
        subject: '测试邮件',
        content: '这是一封测试邮件'
      });

      expect(result.success).toBe(true);
      expect(result.messageId).toBeDefined();
      expect(result.onSent).toBe(true);
    });

    test('应该验证邮箱格式', () => {
      const result = node.execute({
        trigger: true,
        to: 'invalid-email',
        subject: '测试邮件',
        content: '这是一封测试邮件'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('无效的邮箱地址');
    });
  });

  describe('PushNotificationNode', () => {
    let node: PushNotificationNode;

    beforeEach(() => {
      node = new PushNotificationNode();
    });

    test('应该正确创建推送通知节点', () => {
      expect(node).toBeDefined();
      expect(node.nodeType).toBe('PushNotification');
      expect(node.name).toBe('推送通知');
    });

    test('应该能够发送推送通知', () => {
      const result = node.execute({
        trigger: true,
        deviceToken: 'valid_device_token_12345678901234567890',
        title: '测试推送',
        body: '这是一条测试推送消息'
      });

      expect(result.success).toBe(true);
      expect(result.messageId).toBeDefined();
      expect(result.successCount).toBeGreaterThan(0);
    });
  });

  describe('NotificationTemplateNode', () => {
    let node: NotificationTemplateNode;

    beforeEach(() => {
      node = new NotificationTemplateNode();
    });

    test('应该能够创建通知模板', () => {
      const result = node.execute({
        action: 'create',
        templateName: '测试模板',
        templateContent: '欢迎 {{name}}！',
        variables: ['name']
      });

      expect(result.success).toBe(true);
      expect(result.templateId).toBeDefined();
      expect(result.onCreated).toBe(true);
    });

    test('应该能够渲染模板', () => {
      const result = node.execute({
        action: 'render',
        templateId: 'test_template',
        metadata: { name: '张三' }
      });

      expect(result.success).toBe(true);
      expect(result.template.renderedContent).toContain('张三');
    });
  });
});

describe('批次2.1 监控服务节点测试', () => {
  describe('SystemMonitoringNode', () => {
    let node: SystemMonitoringNode;

    beforeEach(() => {
      node = new SystemMonitoringNode();
    });

    test('应该正确创建系统监控节点', () => {
      expect(node).toBeDefined();
      expect(node.nodeType).toBe('SystemMonitoring');
      expect(node.name).toBe('系统监控');
    });

    test('应该能够监控系统资源', () => {
      const result = node.execute({
        trigger: true,
        metrics: ['cpu', 'memory', 'disk']
      });

      expect(result.success).toBe(true);
      expect(result.metrics).toBeDefined();
      expect(result.cpuUsage).toBeGreaterThanOrEqual(0);
      expect(result.memoryUsage).toBeGreaterThanOrEqual(0);
    });
  });

  describe('ErrorTrackingNode', () => {
    let node: ErrorTrackingNode;

    beforeEach(() => {
      node = new ErrorTrackingNode();
    });

    test('应该能够跟踪错误', () => {
      const result = node.execute({
        action: 'track',
        error: {
          message: '测试错误',
          type: 'TestError',
          level: 'error'
        },
        context: { userId: 'user123' }
      });

      expect(result.success).toBe(true);
      expect(result.errorId).toBeDefined();
      expect(result.onErrorDetected).toBe(true);
    });
  });

  describe('AlertSystemNode', () => {
    let node: AlertSystemNode;

    beforeEach(() => {
      node = new AlertSystemNode();
    });

    test('应该能够创建告警', () => {
      const result = node.execute({
        action: 'create',
        alertRule: {
          title: '测试告警',
          description: '这是一个测试告警'
        },
        severity: 'warning'
      });

      expect(result.success).toBe(true);
      expect(result.alertId).toBeDefined();
      expect(result.onAlertTriggered).toBe(true);
    });
  });
});

describe('批次2.2 后处理效果节点测试', () => {
  describe('BloomEffectNode', () => {
    let node: BloomEffectNode;

    beforeEach(() => {
      node = new BloomEffectNode();
    });

    test('应该正确创建泛光效果节点', () => {
      expect(node).toBeDefined();
      expect(node.nodeType).toBe('BloomEffect');
      expect(node.name).toBe('泛光效果');
    });

    test('应该能够应用泛光效果', () => {
      const result = node.execute({
        trigger: true,
        enabled: true,
        threshold: 1.0,
        strength: 1.5,
        radius: 1.0
      });

      expect(result.success).toBe(true);
      expect(result.effectId).toBeDefined();
      expect(result.onApplied).toBe(true);
    });
  });

  describe('ColorGradingNode', () => {
    let node: ColorGradingNode;

    beforeEach(() => {
      node = new ColorGradingNode();
    });

    test('应该能够应用颜色分级', () => {
      const result = node.execute({
        trigger: true,
        enabled: true,
        brightness: 0.1,
        contrast: 1.2,
        saturation: 1.1
      });

      expect(result.success).toBe(true);
      expect(result.effectId).toBeDefined();
      expect(result.isActive).toBe(true);
    });
  });

  describe('SSAONode', () => {
    let node: SSAONode;

    beforeEach(() => {
      node = new SSAONode();
    });

    test('应该能够应用SSAO效果', () => {
      const result = node.execute({
        trigger: true,
        enabled: true,
        radius: 0.5,
        intensity: 1.0
      });

      expect(result.success).toBe(true);
      expect(result.effectId).toBeDefined();
      expect(result.onApplied).toBe(true);
    });
  });

  describe('CustomPostProcessNode', () => {
    let node: CustomPostProcessNode;

    beforeEach(() => {
      node = new CustomPostProcessNode();
    });

    test('应该能够应用自定义后处理', () => {
      const result = node.execute({
        trigger: true,
        enabled: true,
        shaderCode: 'vec4 customEffect(vec4 color) { return color * 1.2; }',
        uniforms: { intensity: 1.0 }
      });

      expect(result.success).toBe(true);
      expect(result.effectId).toBeDefined();
    });

    test('应该验证着色器代码', () => {
      const result = node.execute({
        trigger: true,
        enabled: true,
        shaderCode: '',
        uniforms: {}
      });

      expect(result.success).toBe(false);
    });
  });
});

describe('节点集成测试', () => {
  test('所有节点应该有正确的类型和名称', () => {
    const nodes = [
      // 通知服务节点
      { class: EmailNotificationNode, type: 'EmailNotification', name: '邮件通知' },
      { class: PushNotificationNode, type: 'PushNotification', name: '推送通知' },
      { class: SMSNotificationNode, type: 'SMSNotification', name: '短信通知' },
      { class: InAppNotificationNode, type: 'InAppNotification', name: '应用内通知' },
      { class: NotificationTemplateNode, type: 'NotificationTemplate', name: '通知模板' },
      { class: NotificationScheduleNode, type: 'NotificationSchedule', name: '通知调度' },
      { class: NotificationAnalyticsNode, type: 'NotificationAnalytics', name: '通知分析' },
      { class: NotificationPreferencesNode, type: 'NotificationPreferences', name: '通知偏好' },
      
      // 监控服务节点
      { class: SystemMonitoringNode, type: 'SystemMonitoring', name: '系统监控' },
      { class: PerformanceMonitoringNode, type: 'PerformanceMonitoring', name: '性能监控' },
      { class: ErrorTrackingNode, type: 'ErrorTracking', name: '错误跟踪' },
      { class: LogAnalysisNode, type: 'LogAnalysis', name: '日志分析' },
      { class: AlertSystemNode, type: 'AlertSystem', name: '告警系统' },
      
      // 后处理效果节点
      { class: BloomEffectNode, type: 'BloomEffect', name: '泛光效果' },
      { class: BlurEffectNode, type: 'BlurEffect', name: '模糊效果' },
      { class: ColorGradingNode, type: 'ColorGrading', name: '颜色分级' },
      { class: ToneMappingNode, type: 'ToneMapping', name: '色调映射' },
      { class: SSAONode, type: 'SSAO', name: 'SSAO' },
      { class: SSRNode, type: 'SSR', name: 'SSR' },
      { class: MotionBlurNode, type: 'MotionBlur', name: '运动模糊' },
      { class: DepthOfFieldNode, type: 'DepthOfField', name: '景深' },
      { class: FilmGrainNode, type: 'FilmGrain', name: '胶片颗粒' },
      { class: VignetteNode, type: 'Vignette', name: '暗角效果' },
      { class: ChromaticAberrationNode, type: 'ChromaticAberration', name: '色差' },
      { class: LensDistortionNode, type: 'LensDistortion', name: '镜头畸变' },
      { class: AntiAliasingNode, type: 'AntiAliasing', name: '抗锯齿' },
      { class: HDRProcessingNode, type: 'HDRProcessing', name: 'HDR处理' },
      { class: CustomPostProcessNode, type: 'CustomPostProcess', name: '自定义后处理' }
    ];

    nodes.forEach(({ class: NodeClass, type, name }) => {
      const node = new NodeClass();
      expect(node.nodeType).toBe(type);
      expect(node.name).toBe(name);
    });
  });

  test('所有节点应该能够正确执行', () => {
    const testNodes = [
      new EmailNotificationNode(),
      new SystemMonitoringNode(),
      new BloomEffectNode()
    ];

    testNodes.forEach(node => {
      const result = node.execute({});
      expect(result).toBeDefined();
      expect(typeof result.success).toBe('boolean');
    });
  });
});
