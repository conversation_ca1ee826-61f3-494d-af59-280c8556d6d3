/**
 * 监控服务节点集合 - 第二部分
 * 批次2.1 - 服务器集成节点
 * 提供错误跟踪、日志分析、告警系统等功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { MonitoringServiceNode } from './MonitoringServiceNodes';

/**
 * 错误跟踪节点
 * 批次2.1 - 监控服务节点
 */
export class ErrorTrackingNode extends MonitoringServiceNode {
  public static readonly TYPE = 'ErrorTracking';
  public static readonly NAME = '错误跟踪';
  public static readonly DESCRIPTION = '跟踪和分析应用错误';

  constructor(nodeType: string = ErrorTrackingNode.TYPE, name: string = ErrorTrackingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '操作类型');
    this.addInput('error', 'object', '错误对象');
    this.addInput('context', 'object', '错误上下文');
    this.addInput('userId', 'string', '用户ID');
    this.addInput('sessionId', 'string', '会话ID');
    this.addInput('timeRange', 'object', '时间范围');
    this.addInput('filters', 'object', '过滤条件');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('data', 'object', '错误数据');
    this.addOutput('metrics', 'object', '错误指标');
    this.addOutput('status', 'string', '跟踪状态');
    this.addOutput('error', 'string', '错误信息');
    this.addOutput('timestamp', 'string', '时间戳');
    this.addOutput('errorId', 'string', '错误ID');
    this.addOutput('errorCount', 'number', '错误数量');
    this.addOutput('errorRate', 'number', '错误率');
    this.addOutput('trends', 'array', '错误趋势');
    this.addOutput('onErrorDetected', 'trigger', '错误检测事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'track';
      const error = inputs?.error || {};
      const context = inputs?.context || {};
      const userId = inputs?.userId as string;
      const sessionId = inputs?.sessionId as string;
      const timeRange = inputs?.timeRange || {};
      const filters = inputs?.filters || {};

      // 执行错误跟踪操作
      const result = this.handleErrorTracking(action, {
        error, context, userId, sessionId, timeRange, filters
      });

      Debug.log('ErrorTrackingNode', `错误跟踪${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('ErrorTrackingNode', '错误跟踪失败', error);
      return this.getErrorOutputs(error instanceof Error ? error.message : '错误跟踪失败');
    }
  }

  private handleErrorTracking(action: string, params: any): any {
    switch (action) {
      case 'track':
        return this.trackError(params);
      case 'get':
        return this.getErrors(params);
      case 'analyze':
        return this.analyzeErrors(params);
      case 'resolve':
        return this.resolveError(params);
      case 'trends':
        return this.getErrorTrends(params);
      default:
        return this.getErrorOutputs('不支持的操作类型');
    }
  }

  private trackError(params: any): any {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const errorData = {
      id: errorId,
      message: params.error.message || '未知错误',
      stack: params.error.stack || '',
      type: params.error.type || 'Error',
      level: params.error.level || 'error',
      userId: params.userId,
      sessionId: params.sessionId,
      context: params.context,
      timestamp: new Date().toISOString(),
      resolved: false,
      occurrences: 1
    };

    // 模拟错误分类
    const category = this.categorizeError(errorData);
    errorData.category = category;

    return {
      success: true,
      data: errorData,
      metrics: {
        errorId,
        category,
        severity: this.calculateSeverity(errorData)
      },
      status: 'tracked',
      error: '',
      timestamp: errorData.timestamp,
      errorId,
      errorCount: 1,
      errorRate: 0.1,
      trends: [],
      onErrorDetected: true
    };
  }

  private getErrors(params: any): any {
    // 模拟错误列表
    const errors = this.generateMockErrors(50);
    
    // 应用过滤器
    let filteredErrors = errors;
    if (params.filters) {
      filteredErrors = this.applyFilters(errors, params.filters);
    }

    // 应用时间范围
    if (params.timeRange) {
      filteredErrors = this.applyTimeRange(filteredErrors, params.timeRange);
    }

    const metrics = this.calculateErrorMetrics(filteredErrors);

    return {
      success: true,
      data: { errors: filteredErrors },
      metrics,
      status: 'retrieved',
      error: '',
      timestamp: new Date().toISOString(),
      errorId: '',
      errorCount: filteredErrors.length,
      errorRate: metrics.errorRate,
      trends: [],
      onErrorDetected: false
    };
  }

  private analyzeErrors(params: any): any {
    const errors = this.generateMockErrors(100);
    const analysis = this.performErrorAnalysis(errors);

    return {
      success: true,
      data: analysis,
      metrics: analysis.metrics,
      status: 'analyzed',
      error: '',
      timestamp: new Date().toISOString(),
      errorId: '',
      errorCount: errors.length,
      errorRate: analysis.metrics.errorRate,
      trends: analysis.trends,
      onErrorDetected: false
    };
  }

  private resolveError(params: any): any {
    if (!params.error.id) {
      return this.getErrorOutputs('错误ID不能为空');
    }

    const resolvedError = {
      id: params.error.id,
      resolved: true,
      resolvedAt: new Date().toISOString(),
      resolvedBy: params.userId || 'system'
    };

    return {
      success: true,
      data: resolvedError,
      metrics: { resolved: true },
      status: 'resolved',
      error: '',
      timestamp: new Date().toISOString(),
      errorId: params.error.id,
      errorCount: 0,
      errorRate: 0,
      trends: [],
      onErrorDetected: false
    };
  }

  private getErrorTrends(params: any): any {
    const trends = this.generateErrorTrends(30);
    const analysis = this.analyzeTrends(trends);

    return {
      success: true,
      data: { trends, analysis },
      metrics: analysis.metrics,
      status: 'trends_analyzed',
      error: '',
      timestamp: new Date().toISOString(),
      errorId: '',
      errorCount: 0,
      errorRate: analysis.metrics.averageErrorRate,
      trends,
      onErrorDetected: false
    };
  }

  private categorizeError(error: any): string {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('connection')) {
      return 'network';
    } else if (message.includes('database') || message.includes('sql')) {
      return 'database';
    } else if (message.includes('auth') || message.includes('permission')) {
      return 'authentication';
    } else if (message.includes('validation') || message.includes('invalid')) {
      return 'validation';
    } else {
      return 'application';
    }
  }

  private calculateSeverity(error: any): string {
    if (error.level === 'critical' || error.type === 'FatalError') {
      return 'critical';
    } else if (error.level === 'error' || error.category === 'database') {
      return 'high';
    } else if (error.level === 'warning') {
      return 'medium';
    } else {
      return 'low';
    }
  }

  private generateMockErrors(count: number): any[] {
    const errors = [];
    const errorTypes = ['TypeError', 'ReferenceError', 'NetworkError', 'ValidationError', 'DatabaseError'];
    const categories = ['network', 'database', 'authentication', 'validation', 'application'];
    
    for (let i = 0; i < count; i++) {
      const timestamp = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      errors.push({
        id: `error_${timestamp.getTime()}_${i}`,
        message: `错误消息 ${i + 1}`,
        type: errorTypes[Math.floor(Math.random() * errorTypes.length)],
        category: categories[Math.floor(Math.random() * categories.length)],
        level: Math.random() > 0.7 ? 'critical' : Math.random() > 0.5 ? 'error' : 'warning',
        timestamp: timestamp.toISOString(),
        resolved: Math.random() > 0.6,
        occurrences: Math.floor(Math.random() * 10) + 1
      });
    }
    
    return errors.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  private applyFilters(errors: any[], filters: any): any[] {
    return errors.filter(error => {
      if (filters.level && error.level !== filters.level) {
        return false;
      }
      if (filters.category && error.category !== filters.category) {
        return false;
      }
      if (filters.resolved !== undefined && error.resolved !== filters.resolved) {
        return false;
      }
      return true;
    });
  }

  private applyTimeRange(errors: any[], timeRange: any): any[] {
    if (!timeRange.start && !timeRange.end) {
      return errors;
    }

    return errors.filter(error => {
      const errorTime = new Date(error.timestamp);
      if (timeRange.start && errorTime < new Date(timeRange.start)) {
        return false;
      }
      if (timeRange.end && errorTime > new Date(timeRange.end)) {
        return false;
      }
      return true;
    });
  }

  private calculateErrorMetrics(errors: any[]): any {
    const total = errors.length;
    const resolved = errors.filter(e => e.resolved).length;
    const critical = errors.filter(e => e.level === 'critical').length;
    const byCategory = {};

    errors.forEach(error => {
      byCategory[error.category] = (byCategory[error.category] || 0) + 1;
    });

    return {
      total,
      resolved,
      unresolved: total - resolved,
      critical,
      errorRate: total > 0 ? (total / 1000) * 100 : 0, // 假设总请求数为1000
      resolutionRate: total > 0 ? (resolved / total) * 100 : 0,
      byCategory
    };
  }

  private performErrorAnalysis(errors: any[]): any {
    const metrics = this.calculateErrorMetrics(errors);
    const patterns = this.identifyErrorPatterns(errors);
    const hotspots = this.identifyErrorHotspots(errors);
    const trends = this.generateErrorTrends(7);

    return {
      metrics,
      patterns,
      hotspots,
      trends,
      insights: [
        { type: 'warning', message: '数据库错误数量增加了25%' },
        { type: 'info', message: '网络错误主要发生在晚上8-10点' },
        { type: 'suggestion', message: '建议增加验证错误的处理逻辑' }
      ],
      recommendations: [
        '优化数据库查询性能',
        '增加网络重试机制',
        '完善错误处理流程'
      ]
    };
  }

  private identifyErrorPatterns(errors: any[]): any[] {
    const patterns = [];
    
    // 按小时分组统计
    const hourlyStats = {};
    errors.forEach(error => {
      const hour = new Date(error.timestamp).getHours();
      hourlyStats[hour] = (hourlyStats[hour] || 0) + 1;
    });

    // 找出高峰时段
    const peakHour = Object.keys(hourlyStats).reduce((a, b) => 
      hourlyStats[a] > hourlyStats[b] ? a : b
    );

    patterns.push({
      type: 'temporal',
      description: `错误高峰时段: ${peakHour}:00`,
      confidence: 0.85
    });

    return patterns;
  }

  private identifyErrorHotspots(errors: any[]): any[] {
    const hotspots = [];
    
    // 按类别统计
    const categoryStats = {};
    errors.forEach(error => {
      categoryStats[error.category] = (categoryStats[error.category] || 0) + 1;
    });

    // 找出最频繁的错误类别
    const topCategory = Object.keys(categoryStats).reduce((a, b) => 
      categoryStats[a] > categoryStats[b] ? a : b
    );

    hotspots.push({
      type: 'category',
      name: topCategory,
      count: categoryStats[topCategory],
      percentage: (categoryStats[topCategory] / errors.length) * 100
    });

    return hotspots;
  }

  private generateErrorTrends(days: number): any[] {
    const trends = [];
    const now = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      trends.push({
        date: date.toISOString().split('T')[0],
        errorCount: Math.floor(Math.random() * 50) + 10,
        criticalCount: Math.floor(Math.random() * 10),
        resolvedCount: Math.floor(Math.random() * 40) + 5
      });
    }
    
    return trends;
  }

  private analyzeTrends(trends: any[]): any {
    const totalErrors = trends.reduce((sum, day) => sum + day.errorCount, 0);
    const averageErrorRate = totalErrors / trends.length;
    
    // 计算趋势方向
    const recent = trends.slice(-3);
    const earlier = trends.slice(0, 3);
    const recentAvg = recent.reduce((sum, day) => sum + day.errorCount, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, day) => sum + day.errorCount, 0) / earlier.length;
    
    const trendDirection = recentAvg > earlierAvg ? 'increasing' : 'decreasing';
    const changePercentage = ((recentAvg - earlierAvg) / earlierAvg) * 100;

    return {
      metrics: {
        averageErrorRate,
        trendDirection,
        changePercentage: Math.abs(changePercentage)
      },
      insights: [
        {
          type: trendDirection === 'increasing' ? 'warning' : 'positive',
          message: `错误数量${trendDirection === 'increasing' ? '上升' : '下降'}了${Math.abs(changePercentage).toFixed(1)}%`
        }
      ]
    };
  }
}

/**
 * 日志分析节点
 * 批次2.1 - 监控服务节点
 */
export class LogAnalysisNode extends MonitoringServiceNode {
  public static readonly TYPE = 'LogAnalysis';
  public static readonly NAME = '日志分析';
  public static readonly DESCRIPTION = '分析应用日志数据';

  constructor(nodeType: string = LogAnalysisNode.TYPE, name: string = LogAnalysisNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '分析类型');
    this.addInput('logSource', 'string', '日志源');
    this.addInput('timeRange', 'object', '时间范围');
    this.addInput('query', 'string', '查询条件');
    this.addInput('logLevel', 'string', '日志级别');
    this.addInput('filters', 'object', '过滤条件');
    this.addInput('aggregation', 'string', '聚合方式');

    // 输出端口
    this.addOutput('success', 'boolean', '分析成功');
    this.addOutput('data', 'object', '日志数据');
    this.addOutput('metrics', 'object', '日志指标');
    this.addOutput('status', 'string', '分析状态');
    this.addOutput('error', 'string', '错误信息');
    this.addOutput('timestamp', 'string', '时间戳');
    this.addOutput('logCount', 'number', '日志数量');
    this.addOutput('patterns', 'array', '日志模式');
    this.addOutput('anomalies', 'array', '异常检测');
    this.addOutput('insights', 'array', '分析洞察');
    this.addOutput('onAnomalyDetected', 'trigger', '异常检测事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'analyze';
      const logSource = inputs?.logSource as string || 'application';
      const timeRange = inputs?.timeRange || { hours: 24 };
      const query = inputs?.query as string || '';
      const logLevel = inputs?.logLevel as string || 'all';
      const filters = inputs?.filters || {};
      const aggregation = inputs?.aggregation as string || 'count';

      // 执行日志分析
      const result = this.performLogAnalysis(action, {
        logSource, timeRange, query, logLevel, filters, aggregation
      });

      Debug.log('LogAnalysisNode', `日志分析${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('LogAnalysisNode', '日志分析失败', error);
      return this.getErrorOutputs(error instanceof Error ? error.message : '日志分析失败');
    }
  }

  private performLogAnalysis(action: string, params: any): any {
    switch (action) {
      case 'analyze':
        return this.analyzeLogs(params);
      case 'search':
        return this.searchLogs(params);
      case 'patterns':
        return this.detectPatterns(params);
      case 'anomalies':
        return this.detectAnomalies(params);
      case 'metrics':
        return this.calculateLogMetrics(params);
      default:
        return this.getErrorOutputs('不支持的分析类型');
    }
  }

  private analyzeLogs(params: any): any {
    // 模拟日志分析
    const logs = this.generateMockLogs(1000, params);
    const metrics = this.calculateMetrics(logs);
    const patterns = this.findPatterns(logs);
    const anomalies = this.findAnomalies(logs);
    const insights = this.generateInsights(logs, metrics, patterns, anomalies);

    return {
      success: true,
      data: {
        logs: logs.slice(0, 100), // 只返回前100条
        summary: {
          totalLogs: logs.length,
          timeRange: params.timeRange,
          source: params.logSource
        }
      },
      metrics,
      status: 'analyzed',
      error: '',
      timestamp: new Date().toISOString(),
      logCount: logs.length,
      patterns,
      anomalies,
      insights,
      onAnomalyDetected: anomalies.length > 0
    };
  }

  private searchLogs(params: any): any {
    const logs = this.generateMockLogs(500, params);
    const filteredLogs = this.applyLogFilters(logs, params);

    return {
      success: true,
      data: { logs: filteredLogs },
      metrics: { count: filteredLogs.length },
      status: 'searched',
      error: '',
      timestamp: new Date().toISOString(),
      logCount: filteredLogs.length,
      patterns: [],
      anomalies: [],
      insights: [],
      onAnomalyDetected: false
    };
  }

  private detectPatterns(params: any): any {
    const logs = this.generateMockLogs(1000, params);
    const patterns = this.findPatterns(logs);

    return {
      success: true,
      data: { patterns },
      metrics: { patternCount: patterns.length },
      status: 'patterns_detected',
      error: '',
      timestamp: new Date().toISOString(),
      logCount: logs.length,
      patterns,
      anomalies: [],
      insights: [],
      onAnomalyDetected: false
    };
  }

  private detectAnomalies(params: any): any {
    const logs = this.generateMockLogs(1000, params);
    const anomalies = this.findAnomalies(logs);

    return {
      success: true,
      data: { anomalies },
      metrics: { anomalyCount: anomalies.length },
      status: 'anomalies_detected',
      error: '',
      timestamp: new Date().toISOString(),
      logCount: logs.length,
      patterns: [],
      anomalies,
      insights: [],
      onAnomalyDetected: anomalies.length > 0
    };
  }

  private generateMockLogs(count: number, params: any): any[] {
    const logs = [];
    const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
    const sources = ['application', 'database', 'network', 'security'];
    const messages = [
      'User login successful',
      'Database connection established',
      'API request processed',
      'Cache miss occurred',
      'Authentication failed',
      'Network timeout',
      'Memory usage high',
      'Disk space low'
    ];

    for (let i = 0; i < count; i++) {
      const timestamp = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000);
      logs.push({
        id: `log_${timestamp.getTime()}_${i}`,
        timestamp: timestamp.toISOString(),
        level: levels[Math.floor(Math.random() * levels.length)],
        source: sources[Math.floor(Math.random() * sources.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        userId: Math.random() > 0.7 ? `user_${Math.floor(Math.random() * 1000)}` : null,
        sessionId: `session_${Math.floor(Math.random() * 10000)}`,
        metadata: {
          ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
          userAgent: 'Mozilla/5.0...',
          responseTime: Math.random() * 1000
        }
      });
    }

    return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  private calculateMetrics(logs: any[]): any {
    const byLevel = {};
    const bySource = {};
    const byHour = {};

    logs.forEach(log => {
      // 按级别统计
      byLevel[log.level] = (byLevel[log.level] || 0) + 1;

      // 按源统计
      bySource[log.source] = (bySource[log.source] || 0) + 1;

      // 按小时统计
      const hour = new Date(log.timestamp).getHours();
      byHour[hour] = (byHour[hour] || 0) + 1;
    });

    return {
      total: logs.length,
      byLevel,
      bySource,
      byHour,
      errorRate: (byLevel['ERROR'] || 0) / logs.length * 100,
      averagePerHour: logs.length / 24
    };
  }

  private findPatterns(logs: any[]): any[] {
    const patterns = [];

    // 检测重复错误模式
    const errorMessages = {};
    logs.filter(log => log.level === 'ERROR').forEach(log => {
      errorMessages[log.message] = (errorMessages[log.message] || 0) + 1;
    });

    Object.entries(errorMessages).forEach(([message, count]) => {
      if (count > 5) {
        patterns.push({
          type: 'repeated_error',
          message,
          count,
          severity: 'high',
          description: `错误消息重复出现${count}次`
        });
      }
    });

    // 检测时间模式
    const hourlyActivity = {};
    logs.forEach(log => {
      const hour = new Date(log.timestamp).getHours();
      hourlyActivity[hour] = (hourlyActivity[hour] || 0) + 1;
    });

    const peakHour = Object.keys(hourlyActivity).reduce((a, b) =>
      hourlyActivity[a] > hourlyActivity[b] ? a : b
    );

    patterns.push({
      type: 'peak_activity',
      hour: peakHour,
      count: hourlyActivity[peakHour],
      severity: 'info',
      description: `活动高峰时段: ${peakHour}:00`
    });

    return patterns;
  }

  private findAnomalies(logs: any[]): any[] {
    const anomalies = [];

    // 检测错误率异常
    const errorLogs = logs.filter(log => log.level === 'ERROR');
    const errorRate = (errorLogs.length / logs.length) * 100;

    if (errorRate > 10) {
      anomalies.push({
        type: 'high_error_rate',
        value: errorRate,
        threshold: 10,
        severity: 'critical',
        description: `错误率异常高: ${errorRate.toFixed(2)}%`,
        timestamp: new Date().toISOString()
      });
    }

    // 检测响应时间异常
    const responseTimes = logs
      .filter(log => log.metadata?.responseTime)
      .map(log => log.metadata.responseTime);

    if (responseTimes.length > 0) {
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      if (avgResponseTime > 1000) {
        anomalies.push({
          type: 'slow_response',
          value: avgResponseTime,
          threshold: 1000,
          severity: 'warning',
          description: `平均响应时间过长: ${avgResponseTime.toFixed(2)}ms`,
          timestamp: new Date().toISOString()
        });
      }
    }

    return anomalies;
  }

  private generateInsights(logs: any[], metrics: any, patterns: any[], anomalies: any[]): any[] {
    const insights = [];

    // 基于指标的洞察
    if (metrics.errorRate > 5) {
      insights.push({
        type: 'warning',
        category: 'error_rate',
        message: `错误率较高(${metrics.errorRate.toFixed(2)}%)，建议检查应用状态`,
        priority: 'high'
      });
    }

    // 基于模式的洞察
    patterns.forEach(pattern => {
      if (pattern.type === 'repeated_error') {
        insights.push({
          type: 'suggestion',
          category: 'error_pattern',
          message: `检测到重复错误模式，建议修复: ${pattern.message}`,
          priority: 'medium'
        });
      }
    });

    // 基于异常的洞察
    anomalies.forEach(anomaly => {
      insights.push({
        type: 'alert',
        category: 'anomaly',
        message: anomaly.description,
        priority: anomaly.severity === 'critical' ? 'high' : 'medium'
      });
    });

    return insights;
  }

  private applyLogFilters(logs: any[], params: any): any[] {
    let filtered = logs;

    if (params.logLevel && params.logLevel !== 'all') {
      filtered = filtered.filter(log => log.level === params.logLevel.toUpperCase());
    }

    if (params.query) {
      const query = params.query.toLowerCase();
      filtered = filtered.filter(log =>
        log.message.toLowerCase().includes(query) ||
        log.source.toLowerCase().includes(query)
      );
    }

    if (params.filters) {
      if (params.filters.source) {
        filtered = filtered.filter(log => log.source === params.filters.source);
      }
      if (params.filters.userId) {
        filtered = filtered.filter(log => log.userId === params.filters.userId);
      }
    }

    return filtered;
  }
}

/**
 * 告警系统节点
 * 批次2.1 - 监控服务节点
 */
export class AlertSystemNode extends MonitoringServiceNode {
  public static readonly TYPE = 'AlertSystem';
  public static readonly NAME = '告警系统';
  public static readonly DESCRIPTION = '管理系统告警和通知';

  constructor(nodeType: string = AlertSystemNode.TYPE, name: string = AlertSystemNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '操作类型');
    this.addInput('alertRule', 'object', '告警规则');
    this.addInput('alertId', 'string', '告警ID');
    this.addInput('severity', 'string', '严重程度');
    this.addInput('condition', 'object', '触发条件');
    this.addInput('notification', 'object', '通知设置');
    this.addInput('timeRange', 'object', '时间范围');
    this.addInput('filters', 'object', '过滤条件');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('data', 'object', '告警数据');
    this.addOutput('metrics', 'object', '告警指标');
    this.addOutput('status', 'string', '告警状态');
    this.addOutput('error', 'string', '错误信息');
    this.addOutput('timestamp', 'string', '时间戳');
    this.addOutput('alertId', 'string', '告警ID');
    this.addOutput('alertCount', 'number', '告警数量');
    this.addOutput('activeAlerts', 'array', '活跃告警');
    this.addOutput('rules', 'array', '告警规则');
    this.addOutput('onAlertTriggered', 'trigger', '告警触发事件');
    this.addOutput('onAlertResolved', 'trigger', '告警解决事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'list';
      const alertRule = inputs?.alertRule || {};
      const alertId = inputs?.alertId as string;
      const severity = inputs?.severity as string;
      const condition = inputs?.condition || {};
      const notification = inputs?.notification || {};
      const timeRange = inputs?.timeRange || {};
      const filters = inputs?.filters || {};

      // 执行告警系统操作
      const result = this.handleAlertSystem(action, {
        alertRule, alertId, severity, condition, notification, timeRange, filters
      });

      Debug.log('AlertSystemNode', `告警系统${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('AlertSystemNode', '告警系统操作失败', error);
      return this.getErrorOutputs(error instanceof Error ? error.message : '告警系统操作失败');
    }
  }

  private handleAlertSystem(action: string, params: any): any {
    switch (action) {
      case 'create':
        return this.createAlert(params);
      case 'update':
        return this.updateAlert(params);
      case 'resolve':
        return this.resolveAlert(params);
      case 'list':
        return this.listAlerts(params);
      case 'createRule':
        return this.createAlertRule(params);
      case 'updateRule':
        return this.updateAlertRule(params);
      case 'deleteRule':
        return this.deleteAlertRule(params);
      case 'listRules':
        return this.listAlertRules(params);
      case 'test':
        return this.testAlertRule(params);
      default:
        return this.getErrorOutputs('不支持的操作类型');
    }
  }

  private createAlert(params: any): any {
    const alertId = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const alert = {
      id: alertId,
      title: params.alertRule.title || '系统告警',
      description: params.alertRule.description || '检测到系统异常',
      severity: params.severity || 'warning',
      status: 'active',
      source: params.alertRule.source || 'system',
      condition: params.condition,
      triggeredAt: new Date().toISOString(),
      resolvedAt: null,
      acknowledgedAt: null,
      metadata: params.alertRule.metadata || {}
    };

    return {
      success: true,
      data: alert,
      metrics: { alertsCreated: 1 },
      status: 'created',
      error: '',
      timestamp: alert.triggeredAt,
      alertId,
      alertCount: 1,
      activeAlerts: [alert],
      rules: [],
      onAlertTriggered: true,
      onAlertResolved: false
    };
  }

  private updateAlert(params: any): any {
    if (!params.alertId) {
      return this.getErrorOutputs('告警ID不能为空');
    }

    const updatedAlert = {
      id: params.alertId,
      status: params.alertRule.status || 'active',
      acknowledgedAt: params.alertRule.acknowledged ? new Date().toISOString() : null,
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: updatedAlert,
      metrics: { alertsUpdated: 1 },
      status: 'updated',
      error: '',
      timestamp: updatedAlert.updatedAt,
      alertId: params.alertId,
      alertCount: 1,
      activeAlerts: [],
      rules: [],
      onAlertTriggered: false,
      onAlertResolved: false
    };
  }

  private resolveAlert(params: any): any {
    if (!params.alertId) {
      return this.getErrorOutputs('告警ID不能为空');
    }

    const resolvedAlert = {
      id: params.alertId,
      status: 'resolved',
      resolvedAt: new Date().toISOString(),
      resolution: params.alertRule.resolution || '问题已修复'
    };

    return {
      success: true,
      data: resolvedAlert,
      metrics: { alertsResolved: 1 },
      status: 'resolved',
      error: '',
      timestamp: resolvedAlert.resolvedAt,
      alertId: params.alertId,
      alertCount: 0,
      activeAlerts: [],
      rules: [],
      onAlertTriggered: false,
      onAlertResolved: true
    };
  }

  private listAlerts(params: any): any {
    const alerts = this.generateMockAlerts(20);
    const filteredAlerts = this.applyAlertFilters(alerts, params);
    const metrics = this.calculateAlertMetrics(filteredAlerts);

    return {
      success: true,
      data: { alerts: filteredAlerts },
      metrics,
      status: 'listed',
      error: '',
      timestamp: new Date().toISOString(),
      alertId: '',
      alertCount: filteredAlerts.length,
      activeAlerts: filteredAlerts.filter(a => a.status === 'active'),
      rules: [],
      onAlertTriggered: false,
      onAlertResolved: false
    };
  }

  private createAlertRule(params: any): any {
    if (!params.alertRule.name || !params.condition) {
      return this.getErrorOutputs('规则名称和条件不能为空');
    }

    const ruleId = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const rule = {
      id: ruleId,
      name: params.alertRule.name,
      description: params.alertRule.description || '',
      condition: params.condition,
      severity: params.severity || 'warning',
      notification: params.notification,
      enabled: params.alertRule.enabled ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: rule,
      metrics: { rulesCreated: 1 },
      status: 'rule_created',
      error: '',
      timestamp: rule.createdAt,
      alertId: '',
      alertCount: 0,
      activeAlerts: [],
      rules: [rule],
      onAlertTriggered: false,
      onAlertResolved: false
    };
  }

  private updateAlertRule(params: any): any {
    if (!params.alertRule.id) {
      return this.getErrorOutputs('规则ID不能为空');
    }

    const updatedRule = {
      id: params.alertRule.id,
      name: params.alertRule.name,
      description: params.alertRule.description,
      condition: params.condition,
      severity: params.severity,
      notification: params.notification,
      enabled: params.alertRule.enabled,
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: updatedRule,
      metrics: { rulesUpdated: 1 },
      status: 'rule_updated',
      error: '',
      timestamp: updatedRule.updatedAt,
      alertId: '',
      alertCount: 0,
      activeAlerts: [],
      rules: [updatedRule],
      onAlertTriggered: false,
      onAlertResolved: false
    };
  }

  private deleteAlertRule(params: any): any {
    if (!params.alertRule.id) {
      return this.getErrorOutputs('规则ID不能为空');
    }

    return {
      success: true,
      data: { id: params.alertRule.id, deleted: true },
      metrics: { rulesDeleted: 1 },
      status: 'rule_deleted',
      error: '',
      timestamp: new Date().toISOString(),
      alertId: '',
      alertCount: 0,
      activeAlerts: [],
      rules: [],
      onAlertTriggered: false,
      onAlertResolved: false
    };
  }

  private listAlertRules(params: any): any {
    const rules = this.generateMockAlertRules(10);
    const filteredRules = this.applyRuleFilters(rules, params);

    return {
      success: true,
      data: { rules: filteredRules },
      metrics: { totalRules: filteredRules.length },
      status: 'rules_listed',
      error: '',
      timestamp: new Date().toISOString(),
      alertId: '',
      alertCount: 0,
      activeAlerts: [],
      rules: filteredRules,
      onAlertTriggered: false,
      onAlertResolved: false
    };
  }

  private testAlertRule(params: any): any {
    if (!params.alertRule.id) {
      return this.getErrorOutputs('规则ID不能为空');
    }

    // 模拟规则测试
    const testResult = {
      ruleId: params.alertRule.id,
      testPassed: Math.random() > 0.3, // 70% 通过率
      executionTime: Math.random() * 100 + 50, // 50-150ms
      triggeredConditions: Math.floor(Math.random() * 3),
      testData: {
        sampleMetrics: {
          cpu: 75,
          memory: 80,
          disk: 60
        },
        evaluationResult: 'condition_met'
      }
    };

    return {
      success: true,
      data: testResult,
      metrics: { testsExecuted: 1 },
      status: 'rule_tested',
      error: '',
      timestamp: new Date().toISOString(),
      alertId: '',
      alertCount: 0,
      activeAlerts: [],
      rules: [],
      onAlertTriggered: testResult.testPassed,
      onAlertResolved: false
    };
  }

  private generateMockAlerts(count: number): any[] {
    const alerts = [];
    const severities = ['info', 'warning', 'error', 'critical'];
    const statuses = ['active', 'acknowledged', 'resolved'];
    const sources = ['system', 'application', 'network', 'database'];

    for (let i = 0; i < count; i++) {
      const triggeredAt = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      alerts.push({
        id: `alert_${triggeredAt.getTime()}_${i}`,
        title: `告警 ${i + 1}`,
        description: `这是第 ${i + 1} 个告警`,
        severity: severities[Math.floor(Math.random() * severities.length)],
        status,
        source: sources[Math.floor(Math.random() * sources.length)],
        triggeredAt: triggeredAt.toISOString(),
        resolvedAt: status === 'resolved' ? new Date(triggeredAt.getTime() + Math.random() * 24 * 60 * 60 * 1000).toISOString() : null,
        acknowledgedAt: status === 'acknowledged' ? new Date(triggeredAt.getTime() + Math.random() * 60 * 60 * 1000).toISOString() : null
      });
    }

    return alerts.sort((a, b) => new Date(b.triggeredAt).getTime() - new Date(a.triggeredAt).getTime());
  }

  private generateMockAlertRules(count: number): any[] {
    const rules = [];
    const conditions = ['cpu > 80', 'memory > 85', 'disk > 90', 'error_rate > 5'];
    const severities = ['warning', 'error', 'critical'];

    for (let i = 0; i < count; i++) {
      rules.push({
        id: `rule_${Date.now()}_${i}`,
        name: `规则 ${i + 1}`,
        description: `这是第 ${i + 1} 个告警规则`,
        condition: conditions[Math.floor(Math.random() * conditions.length)],
        severity: severities[Math.floor(Math.random() * severities.length)],
        enabled: Math.random() > 0.2, // 80% 启用
        createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
      });
    }

    return rules;
  }

  private applyAlertFilters(alerts: any[], params: any): any[] {
    let filtered = alerts;

    if (params.severity) {
      filtered = filtered.filter(alert => alert.severity === params.severity);
    }

    if (params.filters) {
      if (params.filters.status) {
        filtered = filtered.filter(alert => alert.status === params.filters.status);
      }
      if (params.filters.source) {
        filtered = filtered.filter(alert => alert.source === params.filters.source);
      }
    }

    if (params.timeRange) {
      if (params.timeRange.start) {
        filtered = filtered.filter(alert =>
          new Date(alert.triggeredAt) >= new Date(params.timeRange.start)
        );
      }
      if (params.timeRange.end) {
        filtered = filtered.filter(alert =>
          new Date(alert.triggeredAt) <= new Date(params.timeRange.end)
        );
      }
    }

    return filtered;
  }

  private applyRuleFilters(rules: any[], params: any): any[] {
    let filtered = rules;

    if (params.filters) {
      if (params.filters.enabled !== undefined) {
        filtered = filtered.filter(rule => rule.enabled === params.filters.enabled);
      }
      if (params.filters.severity) {
        filtered = filtered.filter(rule => rule.severity === params.filters.severity);
      }
    }

    return filtered;
  }

  private calculateAlertMetrics(alerts: any[]): any {
    const total = alerts.length;
    const active = alerts.filter(a => a.status === 'active').length;
    const resolved = alerts.filter(a => a.status === 'resolved').length;
    const acknowledged = alerts.filter(a => a.status === 'acknowledged').length;

    const bySeverity = {};
    alerts.forEach(alert => {
      bySeverity[alert.severity] = (bySeverity[alert.severity] || 0) + 1;
    });

    return {
      total,
      active,
      resolved,
      acknowledged,
      bySeverity,
      resolutionRate: total > 0 ? (resolved / total) * 100 : 0
    };
  }
}
