/**
 * 高级后处理效果节点集合 - 第二部分
 * 批次2.2 - 高级渲染节点
 * 提供色调映射、SSAO、SSR、运动模糊等高级效果
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { PostProcessEffectNode } from './AdvancedPostProcessingNodes';

/**
 * 色调映射节点
 * 批次2.2 - 后处理效果节点
 */
export class ToneMappingNode extends PostProcessEffectNode {
  public static readonly TYPE = 'ToneMapping';
  public static readonly NAME = '色调映射';
  public static readonly DESCRIPTION = '将HDR图像映射到LDR显示范围';

  constructor(nodeType: string = ToneMappingNode.TYPE, name: string = ToneMappingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('algorithm', 'string', '映射算法');
    this.addInput('exposure', 'number', '曝光度');
    this.addInput('whitePoint', 'number', '白点');
    this.addInput('adaptationRate', 'number', '适应速率');
    this.addInput('minLuminance', 'number', '最小亮度');
    this.addInput('maxLuminance', 'number', '最大亮度');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const algorithm = inputs?.algorithm as string || 'reinhard';
      const exposure = inputs?.exposure as number || 1.0;
      const whitePoint = inputs?.whitePoint as number || 1.0;
      const adaptationRate = inputs?.adaptationRate as number || 1.0;
      const minLuminance = inputs?.minLuminance as number || 0.01;
      const maxLuminance = inputs?.maxLuminance as number || 100.0;

      // 应用色调映射效果
      const result = this.applyToneMapping({
        enabled, algorithm, exposure, whitePoint, adaptationRate, minLuminance, maxLuminance
      });

      Debug.log('ToneMappingNode', `色调映射${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('ToneMappingNode', '色调映射应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyToneMapping(params: any): any {
    const effectId = this.generateEffectId();
    
    // 验证算法类型
    const validAlgorithms = ['linear', 'reinhard', 'cineon', 'aces', 'uncharted2'];
    if (!validAlgorithms.includes(params.algorithm)) {
      Debug.warn('ToneMappingNode', `不支持的色调映射算法: ${params.algorithm}`);
    }

    // 模拟效果应用
    const toneMappingConfig = {
      id: effectId,
      type: 'toneMapping',
      enabled: params.enabled,
      algorithm: params.algorithm,
      exposure: Math.max(0.1, Math.min(10, params.exposure)),
      whitePoint: Math.max(0.1, Math.min(10, params.whitePoint)),
      adaptationRate: Math.max(0.1, Math.min(5, params.adaptationRate)),
      minLuminance: Math.max(0.001, Math.min(1, params.minLuminance)),
      maxLuminance: Math.max(1, Math.min(1000, params.maxLuminance)),
      createdAt: new Date().toISOString()
    };

    // 注册效果
    PostProcessEffectNode.effectManager.addEffect(effectId, toneMappingConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.exposure);
  }
}

/**
 * SSAO节点（屏幕空间环境光遮蔽）
 * 批次2.2 - 后处理效果节点
 */
export class SSAONode extends PostProcessEffectNode {
  public static readonly TYPE = 'SSAO';
  public static readonly NAME = 'SSAO';
  public static readonly DESCRIPTION = '屏幕空间环境光遮蔽效果';

  constructor(nodeType: string = SSAONode.TYPE, name: string = SSAONode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('radius', 'number', '采样半径');
    this.addInput('bias', 'number', '偏移值');
    this.addInput('intensity', 'number', '遮蔽强度');
    this.addInput('scale', 'number', '缩放因子');
    this.addInput('kernelSize', 'number', '核心大小');
    this.addInput('noiseSize', 'number', '噪声大小');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const radius = inputs?.radius as number || 0.5;
      const bias = inputs?.bias as number || 0.025;
      const intensity = inputs?.intensity as number || 1.0;
      const scale = inputs?.scale as number || 1.0;
      const kernelSize = inputs?.kernelSize as number || 64;
      const noiseSize = inputs?.noiseSize as number || 4;

      // 应用SSAO效果
      const result = this.applySSAO({
        enabled, radius, bias, intensity, scale, kernelSize, noiseSize
      });

      Debug.log('SSAONode', `SSAO效果${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('SSAONode', 'SSAO效果应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applySSAO(params: any): any {
    const effectId = this.generateEffectId();
    
    // 模拟效果应用
    const ssaoConfig = {
      id: effectId,
      type: 'ssao',
      enabled: params.enabled,
      radius: Math.max(0.1, Math.min(2, params.radius)),
      bias: Math.max(0.001, Math.min(0.1, params.bias)),
      intensity: Math.max(0, Math.min(3, params.intensity)),
      scale: Math.max(0.1, Math.min(5, params.scale)),
      kernelSize: Math.max(16, Math.min(128, params.kernelSize)),
      noiseSize: Math.max(2, Math.min(8, params.noiseSize)),
      createdAt: new Date().toISOString()
    };

    // 注册效果
    PostProcessEffectNode.effectManager.addEffect(effectId, ssaoConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.intensity);
  }
}

/**
 * SSR节点（屏幕空间反射）
 * 批次2.2 - 后处理效果节点
 */
export class SSRNode extends PostProcessEffectNode {
  public static readonly TYPE = 'SSR';
  public static readonly NAME = 'SSR';
  public static readonly DESCRIPTION = '屏幕空间反射效果';

  constructor(nodeType: string = SSRNode.TYPE, name: string = SSRNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('intensity', 'number', '反射强度');
    this.addInput('maxDistance', 'number', '最大距离');
    this.addInput('thickness', 'number', '厚度');
    this.addInput('steps', 'number', '步进数');
    this.addInput('binarySteps', 'number', '二分步进数');
    this.addInput('fadeEdge', 'number', '边缘淡化');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const intensity = inputs?.intensity as number || 1.0;
      const maxDistance = inputs?.maxDistance as number || 100.0;
      const thickness = inputs?.thickness as number || 0.1;
      const steps = inputs?.steps as number || 32;
      const binarySteps = inputs?.binarySteps as number || 8;
      const fadeEdge = inputs?.fadeEdge as number || 0.1;

      // 应用SSR效果
      const result = this.applySSR({
        enabled, intensity, maxDistance, thickness, steps, binarySteps, fadeEdge
      });

      Debug.log('SSRNode', `SSR效果${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('SSRNode', 'SSR效果应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applySSR(params: any): any {
    const effectId = this.generateEffectId();
    
    // 模拟效果应用
    const ssrConfig = {
      id: effectId,
      type: 'ssr',
      enabled: params.enabled,
      intensity: Math.max(0, Math.min(2, params.intensity)),
      maxDistance: Math.max(10, Math.min(1000, params.maxDistance)),
      thickness: Math.max(0.01, Math.min(1, params.thickness)),
      steps: Math.max(8, Math.min(128, params.steps)),
      binarySteps: Math.max(4, Math.min(32, params.binarySteps)),
      fadeEdge: Math.max(0, Math.min(1, params.fadeEdge)),
      createdAt: new Date().toISOString()
    };

    // 注册效果
    PostProcessEffectNode.effectManager.addEffect(effectId, ssrConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.intensity);
  }
}

/**
 * 运动模糊节点
 * 批次2.2 - 后处理效果节点
 */
export class MotionBlurNode extends PostProcessEffectNode {
  public static readonly TYPE = 'MotionBlur';
  public static readonly NAME = '运动模糊';
  public static readonly DESCRIPTION = '添加运动模糊效果';

  constructor(nodeType: string = MotionBlurNode.TYPE, name: string = MotionBlurNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('intensity', 'number', '模糊强度');
    this.addInput('samples', 'number', '采样数量');
    this.addInput('velocityScale', 'number', '速度缩放');
    this.addInput('maxBlurRadius', 'number', '最大模糊半径');
    this.addInput('shutterAngle', 'number', '快门角度');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const intensity = inputs?.intensity as number || 1.0;
      const samples = inputs?.samples as number || 16;
      const velocityScale = inputs?.velocityScale as number || 1.0;
      const maxBlurRadius = inputs?.maxBlurRadius as number || 32;
      const shutterAngle = inputs?.shutterAngle as number || 180;

      // 应用运动模糊效果
      const result = this.applyMotionBlur({
        enabled, intensity, samples, velocityScale, maxBlurRadius, shutterAngle
      });

      Debug.log('MotionBlurNode', `运动模糊${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('MotionBlurNode', '运动模糊应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyMotionBlur(params: any): any {
    const effectId = this.generateEffectId();
    
    // 模拟效果应用
    const motionBlurConfig = {
      id: effectId,
      type: 'motionBlur',
      enabled: params.enabled,
      intensity: Math.max(0, Math.min(3, params.intensity)),
      samples: Math.max(4, Math.min(64, params.samples)),
      velocityScale: Math.max(0.1, Math.min(5, params.velocityScale)),
      maxBlurRadius: Math.max(1, Math.min(100, params.maxBlurRadius)),
      shutterAngle: Math.max(0, Math.min(360, params.shutterAngle)),
      createdAt: new Date().toISOString()
    };

    // 注册效果
    PostProcessEffectNode.effectManager.addEffect(effectId, motionBlurConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.intensity);
  }
}

/**
 * 景深节点
 * 批次2.2 - 后处理效果节点
 */
export class DepthOfFieldNode extends PostProcessEffectNode {
  public static readonly TYPE = 'DepthOfField';
  public static readonly NAME = '景深';
  public static readonly DESCRIPTION = '添加景深模糊效果';

  constructor(nodeType: string = DepthOfFieldNode.TYPE, name: string = DepthOfFieldNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('focusDistance', 'number', '焦点距离');
    this.addInput('focalLength', 'number', '焦距');
    this.addInput('fStop', 'number', '光圈值');
    this.addInput('maxBlur', 'number', '最大模糊');
    this.addInput('nearBlur', 'number', '近景模糊');
    this.addInput('farBlur', 'number', '远景模糊');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const focusDistance = inputs?.focusDistance as number || 10.0;
      const focalLength = inputs?.focalLength as number || 50.0;
      const fStop = inputs?.fStop as number || 2.8;
      const maxBlur = inputs?.maxBlur as number || 20.0;
      const nearBlur = inputs?.nearBlur as number || 5.0;
      const farBlur = inputs?.farBlur as number || 100.0;

      // 应用景深效果
      const result = this.applyDepthOfField({
        enabled, focusDistance, focalLength, fStop, maxBlur, nearBlur, farBlur
      });

      Debug.log('DepthOfFieldNode', `景深效果${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('DepthOfFieldNode', '景深效果应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyDepthOfField(params: any): any {
    const effectId = this.generateEffectId();
    
    // 模拟效果应用
    const dofConfig = {
      id: effectId,
      type: 'depthOfField',
      enabled: params.enabled,
      focusDistance: Math.max(0.1, Math.min(1000, params.focusDistance)),
      focalLength: Math.max(10, Math.min(200, params.focalLength)),
      fStop: Math.max(1.0, Math.min(22, params.fStop)),
      maxBlur: Math.max(1, Math.min(100, params.maxBlur)),
      nearBlur: Math.max(0.1, Math.min(50, params.nearBlur)),
      farBlur: Math.max(10, Math.min(1000, params.farBlur)),
      createdAt: new Date().toISOString()
    };

    // 注册效果
    PostProcessEffectNode.effectManager.addEffect(effectId, dofConfig);

    // 计算强度基于光圈值（光圈越大，景深效果越强）
    const intensity = Math.min(1, (22 - params.fStop) / 21);

    return this.getSuccessOutputs(effectId, params.enabled, intensity);
  }
}
