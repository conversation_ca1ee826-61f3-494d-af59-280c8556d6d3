/**
 * 高级后处理效果节点集合
 * 批次2.2 - 高级渲染节点
 * 提供专业的后处理效果节点，包括泛光、模糊、颜色分级等
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 后处理效果管理器
 */
class PostProcessEffectManager {
  private effects: Map<string, any> = new Map();
  private renderOrder: string[] = [];

  public addEffect(id: string, config: any): void {
    this.effects.set(id, config);
    if (!this.renderOrder.includes(id)) {
      this.renderOrder.push(id);
    }
  }

  public removeEffect(id: string): boolean {
    const removed = this.effects.delete(id);
    const index = this.renderOrder.indexOf(id);
    if (index > -1) {
      this.renderOrder.splice(index, 1);
    }
    return removed;
  }

  public getEffect(id: string): any {
    return this.effects.get(id);
  }

  public hasEffect(id: string): boolean {
    return this.effects.has(id);
  }

  public updateEffect(id: string, config: any): boolean {
    if (this.effects.has(id)) {
      this.effects.set(id, { ...this.effects.get(id), ...config });
      return true;
    }
    return false;
  }

  public getAllEffects(): Map<string, any> {
    return new Map(this.effects);
  }

  public getRenderOrder(): string[] {
    return [...this.renderOrder];
  }

  public setRenderOrder(order: string[]): void {
    this.renderOrder = order.filter(id => this.effects.has(id));
  }

  public clear(): void {
    this.effects.clear();
    this.renderOrder = [];
  }

  public getEffectCount(): number {
    return this.effects.size;
  }
}

/**
 * 后处理效果基础节点
 */
export abstract class PostProcessEffectNode extends VisualScriptNode {
  protected static effectManager: PostProcessEffectManager = new PostProcessEffectManager();

  protected getDefaultOutputs(): any {
    return {
      success: false,
      effectId: '',
      isActive: false,
      intensity: 0,
      quality: 'medium',
      renderTime: 0,
      onApplied: false,
      onRemoved: false
    };
  }

  protected getSuccessOutputs(effectId: string, isActive: boolean = true, intensity: number = 1.0): any {
    return {
      success: true,
      effectId,
      isActive,
      intensity,
      quality: 'medium',
      renderTime: Math.random() * 5 + 1, // 1-6ms
      onApplied: isActive,
      onRemoved: !isActive
    };
  }

  protected generateEffectId(): string {
    return `effect_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 泛光效果节点
 * 批次2.2 - 后处理效果节点
 */
export class BloomEffectNode extends PostProcessEffectNode {
  public static readonly TYPE = 'BloomEffect';
  public static readonly NAME = '泛光效果';
  public static readonly DESCRIPTION = '添加泛光发光效果';

  constructor(nodeType: string = BloomEffectNode.TYPE, name: string = BloomEffectNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('threshold', 'number', '亮度阈值');
    this.addInput('strength', 'number', '泛光强度');
    this.addInput('radius', 'number', '泛光半径');
    this.addInput('exposure', 'number', '曝光度');
    this.addInput('quality', 'string', '渲染质量');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const threshold = inputs?.threshold as number || 1.0;
      const strength = inputs?.strength as number || 1.0;
      const radius = inputs?.radius as number || 1.0;
      const exposure = inputs?.exposure as number || 1.0;
      const quality = inputs?.quality as string || 'medium';

      // 应用泛光效果
      const result = this.applyBloomEffect({
        enabled, threshold, strength, radius, exposure, quality
      });

      Debug.log('BloomEffectNode', `泛光效果${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('BloomEffectNode', '泛光效果应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyBloomEffect(params: any): any {
    const effectId = this.generateEffectId();
    
    // 验证参数
    if (params.threshold < 0 || params.threshold > 5) {
      Debug.warn('BloomEffectNode', '亮度阈值超出范围 (0-5)');
    }

    if (params.strength < 0 || params.strength > 3) {
      Debug.warn('BloomEffectNode', '泛光强度超出范围 (0-3)');
    }

    // 模拟效果应用
    const bloomConfig = {
      id: effectId,
      type: 'bloom',
      enabled: params.enabled,
      threshold: Math.max(0, Math.min(5, params.threshold)),
      strength: Math.max(0, Math.min(3, params.strength)),
      radius: Math.max(0.1, Math.min(2, params.radius)),
      exposure: Math.max(0.1, Math.min(3, params.exposure)),
      quality: params.quality,
      createdAt: new Date().toISOString()
    };

    // 注册效果
    PostProcessEffectNode.effectManager.addEffect(effectId, bloomConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.strength);
  }
}

/**
 * 模糊效果节点
 * 批次2.2 - 后处理效果节点
 */
export class BlurEffectNode extends PostProcessEffectNode {
  public static readonly TYPE = 'BlurEffect';
  public static readonly NAME = '模糊效果';
  public static readonly DESCRIPTION = '添加模糊效果';

  constructor(nodeType: string = BlurEffectNode.TYPE, name: string = BlurEffectNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('blurType', 'string', '模糊类型');
    this.addInput('radius', 'number', '模糊半径');
    this.addInput('sigma', 'number', '高斯标准差');
    this.addInput('iterations', 'number', '迭代次数');
    this.addInput('direction', 'string', '模糊方向');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const blurType = inputs?.blurType as string || 'gaussian';
      const radius = inputs?.radius as number || 5.0;
      const sigma = inputs?.sigma as number || 2.0;
      const iterations = inputs?.iterations as number || 1;
      const direction = inputs?.direction as string || 'both';

      // 应用模糊效果
      const result = this.applyBlurEffect({
        enabled, blurType, radius, sigma, iterations, direction
      });

      Debug.log('BlurEffectNode', `模糊效果${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('BlurEffectNode', '模糊效果应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyBlurEffect(params: any): any {
    const effectId = this.generateEffectId();
    
    // 验证参数
    const validBlurTypes = ['gaussian', 'box', 'motion', 'radial'];
    if (!validBlurTypes.includes(params.blurType)) {
      Debug.warn('BlurEffectNode', `不支持的模糊类型: ${params.blurType}`);
    }

    // 模拟效果应用
    const blurConfig = {
      id: effectId,
      type: 'blur',
      enabled: params.enabled,
      blurType: params.blurType,
      radius: Math.max(0, Math.min(50, params.radius)),
      sigma: Math.max(0.1, Math.min(10, params.sigma)),
      iterations: Math.max(1, Math.min(10, params.iterations)),
      direction: params.direction,
      createdAt: new Date().toISOString()
    };

    // 注册效果
    PostProcessEffectNode.effectManager.addEffect(effectId, blurConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.radius / 50);
  }
}

/**
 * 颜色分级节点
 * 批次2.2 - 后处理效果节点
 */
export class ColorGradingNode extends PostProcessEffectNode {
  public static readonly TYPE = 'ColorGrading';
  public static readonly NAME = '颜色分级';
  public static readonly DESCRIPTION = '调整图像颜色和色调';

  constructor(nodeType: string = ColorGradingNode.TYPE, name: string = ColorGradingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('brightness', 'number', '亮度');
    this.addInput('contrast', 'number', '对比度');
    this.addInput('saturation', 'number', '饱和度');
    this.addInput('hue', 'number', '色相');
    this.addInput('gamma', 'number', '伽马值');
    this.addInput('temperature', 'number', '色温');
    this.addInput('tint', 'number', '色调');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const brightness = inputs?.brightness as number || 0.0;
      const contrast = inputs?.contrast as number || 1.0;
      const saturation = inputs?.saturation as number || 1.0;
      const hue = inputs?.hue as number || 0.0;
      const gamma = inputs?.gamma as number || 1.0;
      const temperature = inputs?.temperature as number || 0.0;
      const tint = inputs?.tint as number || 0.0;

      // 应用颜色分级效果
      const result = this.applyColorGrading({
        enabled, brightness, contrast, saturation, hue, gamma, temperature, tint
      });

      Debug.log('ColorGradingNode', `颜色分级${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('ColorGradingNode', '颜色分级应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyColorGrading(params: any): any {
    const effectId = this.generateEffectId();
    
    // 模拟效果应用
    const colorGradingConfig = {
      id: effectId,
      type: 'colorGrading',
      enabled: params.enabled,
      brightness: Math.max(-1, Math.min(1, params.brightness)),
      contrast: Math.max(0, Math.min(3, params.contrast)),
      saturation: Math.max(0, Math.min(3, params.saturation)),
      hue: Math.max(-180, Math.min(180, params.hue)),
      gamma: Math.max(0.1, Math.min(3, params.gamma)),
      temperature: Math.max(-100, Math.min(100, params.temperature)),
      tint: Math.max(-100, Math.min(100, params.tint)),
      createdAt: new Date().toISOString()
    };

    // 注册效果
    PostProcessEffectNode.effectManager.addEffect(effectId, colorGradingConfig);

    // 计算整体强度
    const intensity = Math.abs(params.brightness) + 
                     Math.abs(params.contrast - 1) + 
                     Math.abs(params.saturation - 1) + 
                     Math.abs(params.hue) / 180;

    return this.getSuccessOutputs(effectId, params.enabled, Math.min(1, intensity));
  }
}
