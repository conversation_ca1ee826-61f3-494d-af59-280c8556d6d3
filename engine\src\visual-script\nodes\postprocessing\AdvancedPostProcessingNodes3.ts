/**
 * 高级后处理效果节点集合 - 第三部分
 * 批次2.2 - 高级渲染节点
 * 提供胶片颗粒、暗角效果、色差、镜头畸变、抗锯齿、HDR处理、自定义后处理等效果
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { PostProcessEffectNode } from './AdvancedPostProcessingNodes';

/**
 * 胶片颗粒节点
 * 批次2.2 - 后处理效果节点
 */
export class FilmGrainNode extends PostProcessEffectNode {
  public static readonly TYPE = 'FilmGrain';
  public static readonly NAME = '胶片颗粒';
  public static readonly DESCRIPTION = '添加胶片颗粒效果';

  constructor(nodeType: string = FilmGrainNode.TYPE, name: string = FilmGrainNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('intensity', 'number', '颗粒强度');
    this.addInput('size', 'number', '颗粒大小');
    this.addInput('luminance', 'number', '亮度影响');
    this.addInput('colored', 'boolean', '彩色颗粒');
    this.addInput('speed', 'number', '动画速度');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const intensity = inputs?.intensity as number || 0.5;
      const size = inputs?.size as number || 1.0;
      const luminance = inputs?.luminance as number || 1.0;
      const colored = inputs?.colored as boolean ?? false;
      const speed = inputs?.speed as number || 1.0;

      // 应用胶片颗粒效果
      const result = this.applyFilmGrain({
        enabled, intensity, size, luminance, colored, speed
      });

      Debug.log('FilmGrainNode', `胶片颗粒${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('FilmGrainNode', '胶片颗粒应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyFilmGrain(params: any): any {
    const effectId = this.generateEffectId();
    
    const filmGrainConfig = {
      id: effectId,
      type: 'filmGrain',
      enabled: params.enabled,
      intensity: Math.max(0, Math.min(2, params.intensity)),
      size: Math.max(0.1, Math.min(5, params.size)),
      luminance: Math.max(0, Math.min(2, params.luminance)),
      colored: params.colored,
      speed: Math.max(0.1, Math.min(10, params.speed)),
      createdAt: new Date().toISOString()
    };

    PostProcessEffectNode.effectManager.addEffect(effectId, filmGrainConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.intensity);
  }
}

/**
 * 暗角效果节点
 * 批次2.2 - 后处理效果节点
 */
export class VignetteNode extends PostProcessEffectNode {
  public static readonly TYPE = 'Vignette';
  public static readonly NAME = '暗角效果';
  public static readonly DESCRIPTION = '添加暗角效果';

  constructor(nodeType: string = VignetteNode.TYPE, name: string = VignetteNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('intensity', 'number', '暗角强度');
    this.addInput('smoothness', 'number', '平滑度');
    this.addInput('roundness', 'number', '圆度');
    this.addInput('rounded', 'boolean', '圆形暗角');
    this.addInput('color', 'object', '暗角颜色');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const intensity = inputs?.intensity as number || 0.5;
      const smoothness = inputs?.smoothness as number || 0.2;
      const roundness = inputs?.roundness as number || 1.0;
      const rounded = inputs?.rounded as boolean ?? true;
      const color = inputs?.color || { r: 0, g: 0, b: 0 };

      const result = this.applyVignette({
        enabled, intensity, smoothness, roundness, rounded, color
      });

      Debug.log('VignetteNode', `暗角效果${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('VignetteNode', '暗角效果应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyVignette(params: any): any {
    const effectId = this.generateEffectId();
    
    const vignetteConfig = {
      id: effectId,
      type: 'vignette',
      enabled: params.enabled,
      intensity: Math.max(0, Math.min(1, params.intensity)),
      smoothness: Math.max(0, Math.min(1, params.smoothness)),
      roundness: Math.max(0, Math.min(1, params.roundness)),
      rounded: params.rounded,
      color: params.color,
      createdAt: new Date().toISOString()
    };

    PostProcessEffectNode.effectManager.addEffect(effectId, vignetteConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.intensity);
  }
}

/**
 * 色差节点
 * 批次2.2 - 后处理效果节点
 */
export class ChromaticAberrationNode extends PostProcessEffectNode {
  public static readonly TYPE = 'ChromaticAberration';
  public static readonly NAME = '色差';
  public static readonly DESCRIPTION = '添加色差效果';

  constructor(nodeType: string = ChromaticAberrationNode.TYPE, name: string = ChromaticAberrationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('intensity', 'number', '色差强度');
    this.addInput('offset', 'object', '偏移量');
    this.addInput('radialDistortion', 'boolean', '径向扭曲');
    this.addInput('distortionScale', 'number', '扭曲缩放');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const intensity = inputs?.intensity as number || 0.5;
      const offset = inputs?.offset || { x: 0.001, y: 0.001 };
      const radialDistortion = inputs?.radialDistortion as boolean ?? false;
      const distortionScale = inputs?.distortionScale as number || 1.0;

      const result = this.applyChromaticAberration({
        enabled, intensity, offset, radialDistortion, distortionScale
      });

      Debug.log('ChromaticAberrationNode', `色差效果${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('ChromaticAberrationNode', '色差效果应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyChromaticAberration(params: any): any {
    const effectId = this.generateEffectId();
    
    const chromaticAberrationConfig = {
      id: effectId,
      type: 'chromaticAberration',
      enabled: params.enabled,
      intensity: Math.max(0, Math.min(2, params.intensity)),
      offset: {
        x: Math.max(-0.01, Math.min(0.01, params.offset.x)),
        y: Math.max(-0.01, Math.min(0.01, params.offset.y))
      },
      radialDistortion: params.radialDistortion,
      distortionScale: Math.max(0.1, Math.min(5, params.distortionScale)),
      createdAt: new Date().toISOString()
    };

    PostProcessEffectNode.effectManager.addEffect(effectId, chromaticAberrationConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.intensity);
  }
}

/**
 * 镜头畸变节点
 * 批次2.2 - 后处理效果节点
 */
export class LensDistortionNode extends PostProcessEffectNode {
  public static readonly TYPE = 'LensDistortion';
  public static readonly NAME = '镜头畸变';
  public static readonly DESCRIPTION = '添加镜头畸变效果';

  constructor(nodeType: string = LensDistortionNode.TYPE, name: string = LensDistortionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('intensity', 'number', '畸变强度');
    this.addInput('distortionType', 'string', '畸变类型');
    this.addInput('barrelDistortion', 'number', '桶形畸变');
    this.addInput('pincushionDistortion', 'number', '枕形畸变');
    this.addInput('scale', 'number', '缩放');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const intensity = inputs?.intensity as number || 0.5;
      const distortionType = inputs?.distortionType as string || 'barrel';
      const barrelDistortion = inputs?.barrelDistortion as number || 0.1;
      const pincushionDistortion = inputs?.pincushionDistortion as number || 0.0;
      const scale = inputs?.scale as number || 1.0;

      const result = this.applyLensDistortion({
        enabled, intensity, distortionType, barrelDistortion, pincushionDistortion, scale
      });

      Debug.log('LensDistortionNode', `镜头畸变${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('LensDistortionNode', '镜头畸变应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyLensDistortion(params: any): any {
    const effectId = this.generateEffectId();
    
    const lensDistortionConfig = {
      id: effectId,
      type: 'lensDistortion',
      enabled: params.enabled,
      intensity: Math.max(0, Math.min(2, params.intensity)),
      distortionType: params.distortionType,
      barrelDistortion: Math.max(-1, Math.min(1, params.barrelDistortion)),
      pincushionDistortion: Math.max(-1, Math.min(1, params.pincushionDistortion)),
      scale: Math.max(0.1, Math.min(3, params.scale)),
      createdAt: new Date().toISOString()
    };

    PostProcessEffectNode.effectManager.addEffect(effectId, lensDistortionConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.intensity);
  }
}

/**
 * 抗锯齿节点
 * 批次2.2 - 后处理效果节点
 */
export class AntiAliasingNode extends PostProcessEffectNode {
  public static readonly TYPE = 'AntiAliasing';
  public static readonly NAME = '抗锯齿';
  public static readonly DESCRIPTION = '添加抗锯齿效果';

  constructor(nodeType: string = AntiAliasingNode.TYPE, name: string = AntiAliasingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('algorithm', 'string', '抗锯齿算法');
    this.addInput('quality', 'string', '质量设置');
    this.addInput('edgeThreshold', 'number', '边缘阈值');
    this.addInput('edgeThresholdMin', 'number', '最小边缘阈值');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const algorithm = inputs?.algorithm as string || 'FXAA';
      const quality = inputs?.quality as string || 'medium';
      const edgeThreshold = inputs?.edgeThreshold as number || 0.166;
      const edgeThresholdMin = inputs?.edgeThresholdMin as number || 0.0833;

      const result = this.applyAntiAliasing({
        enabled, algorithm, quality, edgeThreshold, edgeThresholdMin
      });

      Debug.log('AntiAliasingNode', `抗锯齿${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('AntiAliasingNode', '抗锯齿应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyAntiAliasing(params: any): any {
    const effectId = this.generateEffectId();
    
    const validAlgorithms = ['FXAA', 'SMAA', 'TAA', 'MSAA'];
    if (!validAlgorithms.includes(params.algorithm)) {
      Debug.warn('AntiAliasingNode', `不支持的抗锯齿算法: ${params.algorithm}`);
    }

    const antiAliasingConfig = {
      id: effectId,
      type: 'antiAliasing',
      enabled: params.enabled,
      algorithm: params.algorithm,
      quality: params.quality,
      edgeThreshold: Math.max(0.063, Math.min(0.333, params.edgeThreshold)),
      edgeThresholdMin: Math.max(0.0312, Math.min(0.25, params.edgeThresholdMin)),
      createdAt: new Date().toISOString()
    };

    PostProcessEffectNode.effectManager.addEffect(effectId, antiAliasingConfig);

    // 抗锯齿强度基于质量设置
    const qualityIntensity = {
      'low': 0.3,
      'medium': 0.6,
      'high': 0.8,
      'ultra': 1.0
    };

    return this.getSuccessOutputs(effectId, params.enabled, qualityIntensity[params.quality] || 0.6);
  }
}

/**
 * HDR处理节点
 * 批次2.2 - 后处理效果节点
 */
export class HDRProcessingNode extends PostProcessEffectNode {
  public static readonly TYPE = 'HDRProcessing';
  public static readonly NAME = 'HDR处理';
  public static readonly DESCRIPTION = '高动态范围图像处理';

  constructor(nodeType: string = HDRProcessingNode.TYPE, name: string = HDRProcessingNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('exposure', 'number', '曝光度');
    this.addInput('adaptationRate', 'number', '适应速率');
    this.addInput('minLuminance', 'number', '最小亮度');
    this.addInput('maxLuminance', 'number', '最大亮度');
    this.addInput('eyeAdaptation', 'boolean', '眼部适应');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const exposure = inputs?.exposure as number || 1.0;
      const adaptationRate = inputs?.adaptationRate as number || 1.0;
      const minLuminance = inputs?.minLuminance as number || 0.01;
      const maxLuminance = inputs?.maxLuminance as number || 100.0;
      const eyeAdaptation = inputs?.eyeAdaptation as boolean ?? true;

      const result = this.applyHDRProcessing({
        enabled, exposure, adaptationRate, minLuminance, maxLuminance, eyeAdaptation
      });

      Debug.log('HDRProcessingNode', `HDR处理${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('HDRProcessingNode', 'HDR处理应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyHDRProcessing(params: any): any {
    const effectId = this.generateEffectId();
    
    const hdrConfig = {
      id: effectId,
      type: 'hdrProcessing',
      enabled: params.enabled,
      exposure: Math.max(0.1, Math.min(10, params.exposure)),
      adaptationRate: Math.max(0.1, Math.min(5, params.adaptationRate)),
      minLuminance: Math.max(0.001, Math.min(1, params.minLuminance)),
      maxLuminance: Math.max(1, Math.min(1000, params.maxLuminance)),
      eyeAdaptation: params.eyeAdaptation,
      createdAt: new Date().toISOString()
    };

    PostProcessEffectNode.effectManager.addEffect(effectId, hdrConfig);

    return this.getSuccessOutputs(effectId, params.enabled, params.exposure);
  }
}

/**
 * 自定义后处理节点
 * 批次2.2 - 后处理效果节点
 */
export class CustomPostProcessNode extends PostProcessEffectNode {
  public static readonly TYPE = 'CustomPostProcess';
  public static readonly NAME = '自定义后处理';
  public static readonly DESCRIPTION = '自定义后处理效果';

  constructor(nodeType: string = CustomPostProcessNode.TYPE, name: string = CustomPostProcessNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发效果');
    this.addInput('enabled', 'boolean', '启用效果');
    this.addInput('shaderCode', 'string', '着色器代码');
    this.addInput('uniforms', 'object', '着色器参数');
    this.addInput('renderTarget', 'string', '渲染目标');
    this.addInput('blendMode', 'string', '混合模式');

    // 输出端口
    this.addOutput('success', 'boolean', '应用成功');
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('isActive', 'boolean', '是否激活');
    this.addOutput('intensity', 'number', '当前强度');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('renderTime', 'number', '渲染耗时');
    this.addOutput('onApplied', 'trigger', '应用成功事件');
    this.addOutput('onRemoved', 'trigger', '移除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const enabled = inputs?.enabled as boolean ?? true;
      const shaderCode = inputs?.shaderCode as string || '';
      const uniforms = inputs?.uniforms || {};
      const renderTarget = inputs?.renderTarget as string || 'screen';
      const blendMode = inputs?.blendMode as string || 'normal';

      const result = this.applyCustomPostProcess({
        enabled, shaderCode, uniforms, renderTarget, blendMode
      });

      Debug.log('CustomPostProcessNode', `自定义后处理${result.success ? '应用成功' : '应用失败'}`);

      return result;
    } catch (error) {
      Debug.error('CustomPostProcessNode', '自定义后处理应用失败', error);
      return this.getDefaultOutputs();
    }
  }

  private applyCustomPostProcess(params: any): any {
    const effectId = this.generateEffectId();
    
    if (!params.shaderCode) {
      Debug.warn('CustomPostProcessNode', '着色器代码为空');
      return this.getDefaultOutputs();
    }

    const customConfig = {
      id: effectId,
      type: 'customPostProcess',
      enabled: params.enabled,
      shaderCode: params.shaderCode,
      uniforms: params.uniforms,
      renderTarget: params.renderTarget,
      blendMode: params.blendMode,
      createdAt: new Date().toISOString()
    };

    PostProcessEffectNode.effectManager.addEffect(effectId, customConfig);

    // 自定义效果强度基于uniforms数量
    const intensity = Math.min(1, Object.keys(params.uniforms).length / 10);

    return this.getSuccessOutputs(effectId, params.enabled, intensity);
  }
}
