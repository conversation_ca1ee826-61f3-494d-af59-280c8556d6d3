/**
 * 通知服务节点集合 - 第二部分
 * 批次2.1 - 服务器集成节点
 * 提供应用内通知、通知模板、通知调度等功能
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { NotificationServiceNode } from './NotificationServiceNodes';

/**
 * 应用内通知节点
 * 批次2.1 - 通知服务节点
 */
export class InAppNotificationNode extends NotificationServiceNode {
  public static readonly TYPE = 'InAppNotification';
  public static readonly NAME = '应用内通知';
  public static readonly DESCRIPTION = '发送应用内通知消息';

  constructor(nodeType: string = InAppNotificationNode.TYPE, name: string = InAppNotificationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('trigger', 'trigger', '触发发送');
    this.addInput('userId', 'string', '用户ID');
    this.addInput('userIds', 'array', '用户ID列表');
    this.addInput('title', 'string', '通知标题');
    this.addInput('content', 'string', '通知内容');
    this.addInput('type', 'string', '通知类型');
    this.addInput('category', 'string', '通知分类');
    this.addInput('priority', 'string', '优先级');
    this.addInput('icon', 'string', '通知图标');
    this.addInput('image', 'string', '通知图片');
    this.addInput('actionUrl', 'string', '点击跳转链接');
    this.addInput('actionData', 'object', '动作数据');
    this.addInput('expireTime', 'string', '过期时间');
    this.addInput('persistent', 'boolean', '是否持久化');

    // 输出端口
    this.addOutput('success', 'boolean', '发送成功');
    this.addOutput('messageId', 'string', '消息ID');
    this.addOutput('status', 'string', '发送状态');
    this.addOutput('error', 'string', '错误信息');
    this.addOutput('deliveryTime', 'number', '发送耗时');
    this.addOutput('recipientCount', 'number', '接收者数量');
    this.addOutput('onSent', 'trigger', '发送成功事件');
    this.addOutput('onRead', 'trigger', '已读事件');
    this.addOutput('onClicked', 'trigger', '点击事件');
  }

  public execute(inputs?: any): any {
    try {
      const trigger = inputs?.trigger;
      if (!trigger) {
        return this.getDefaultOutputs();
      }

      const userId = inputs?.userId as string;
      const userIds = inputs?.userIds as string[] || [];
      const title = inputs?.title as string;
      const content = inputs?.content as string;
      const type = inputs?.type as string || 'info';
      const category = inputs?.category as string || 'general';
      const priority = inputs?.priority as string || 'normal';
      const icon = inputs?.icon as string;
      const image = inputs?.image as string;
      const actionUrl = inputs?.actionUrl as string;
      const actionData = inputs?.actionData || {};
      const expireTime = inputs?.expireTime as string;
      const persistent = inputs?.persistent as boolean ?? true;

      if (!title || !content) {
        return this.getErrorOutputs('标题和内容不能为空');
      }

      // 准备用户ID列表
      const recipients = userId ? [userId] : userIds;
      if (recipients.length === 0) {
        return this.getErrorOutputs('接收者不能为空');
      }

      // 发送应用内通知
      const result = this.sendInAppNotification({
        recipients, title, content, type, category, priority,
        icon, image, actionUrl, actionData, expireTime, persistent
      });

      Debug.log('InAppNotificationNode', `应用内通知发送${result.success ? '成功' : '失败'}: ${recipients.length} 个用户`);

      return result;
    } catch (error) {
      Debug.error('InAppNotificationNode', '应用内通知发送失败', error);
      return this.getErrorOutputs(error instanceof Error ? error.message : '应用内通知发送失败');
    }
  }

  private sendInAppNotification(params: any): any {
    const startTime = Date.now();
    
    try {
      const messageId = `inapp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 验证用户ID
      const validRecipients = params.recipients.filter((id: string) => this.isValidUserId(id));
      if (validRecipients.length === 0) {
        return this.getErrorOutputs('没有有效的用户ID');
      }

      // 创建通知对象
      const notification = {
        id: messageId,
        title: params.title,
        content: params.content,
        type: params.type,
        category: params.category,
        priority: params.priority,
        icon: params.icon,
        image: params.image,
        actionUrl: params.actionUrl,
        actionData: params.actionData,
        expireTime: params.expireTime,
        persistent: params.persistent,
        createdAt: new Date().toISOString(),
        recipients: validRecipients
      };

      // 模拟发送延迟
      const deliveryTime = Date.now() - startTime + Math.random() * 500;

      // 模拟发送结果
      const success = Math.random() > 0.05; // 95% 成功率

      if (success) {
        return {
          success: true,
          messageId,
          status: 'sent',
          error: '',
          deliveryTime,
          recipientCount: validRecipients.length,
          onSent: true,
          onRead: false,
          onClicked: false
        };
      } else {
        return this.getErrorOutputs('应用内通知服务暂时不可用', messageId);
      }
    } catch (error) {
      const deliveryTime = Date.now() - startTime;
      return {
        ...this.getErrorOutputs(error instanceof Error ? error.message : '发送失败'),
        recipientCount: 0
      };
    }
  }

  private isValidUserId(userId: string): boolean {
    // 简化的用户ID验证
    return userId && userId.length > 0 && /^[a-zA-Z0-9_-]+$/.test(userId);
  }
}

/**
 * 通知模板节点
 * 批次2.1 - 通知服务节点
 */
export class NotificationTemplateNode extends NotificationServiceNode {
  public static readonly TYPE = 'NotificationTemplate';
  public static readonly NAME = '通知模板';
  public static readonly DESCRIPTION = '管理通知模板';

  constructor(nodeType: string = NotificationTemplateNode.TYPE, name: string = NotificationTemplateNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('action', 'string', '操作类型');
    this.addInput('templateId', 'string', '模板ID');
    this.addInput('templateName', 'string', '模板名称');
    this.addInput('templateContent', 'string', '模板内容');
    this.addInput('templateType', 'string', '模板类型');
    this.addInput('category', 'string', '模板分类');
    this.addInput('variables', 'array', '模板变量');
    this.addInput('metadata', 'object', '模板元数据');
    this.addInput('isActive', 'boolean', '是否激活');

    // 输出端口
    this.addOutput('success', 'boolean', '操作成功');
    this.addOutput('templateId', 'string', '模板ID');
    this.addOutput('template', 'object', '模板对象');
    this.addOutput('templates', 'array', '模板列表');
    this.addOutput('error', 'string', '错误信息');
    this.addOutput('onCreated', 'trigger', '创建成功事件');
    this.addOutput('onUpdated', 'trigger', '更新成功事件');
    this.addOutput('onDeleted', 'trigger', '删除成功事件');
  }

  public execute(inputs?: any): any {
    try {
      const action = inputs?.action as string || 'get';
      const templateId = inputs?.templateId as string;
      const templateName = inputs?.templateName as string;
      const templateContent = inputs?.templateContent as string;
      const templateType = inputs?.templateType as string || 'text';
      const category = inputs?.category as string || 'general';
      const variables = inputs?.variables as string[] || [];
      const metadata = inputs?.metadata || {};
      const isActive = inputs?.isActive as boolean ?? true;

      // 执行模板操作
      const result = this.handleTemplateOperation(action, {
        templateId, templateName, templateContent, templateType,
        category, variables, metadata, isActive
      });

      Debug.log('NotificationTemplateNode', `模板操作${result.success ? '成功' : '失败'}: ${action}`);

      return result;
    } catch (error) {
      Debug.error('NotificationTemplateNode', '模板操作失败', error);
      return this.getErrorOutputs(error instanceof Error ? error.message : '模板操作失败');
    }
  }

  private handleTemplateOperation(action: string, params: any): any {
    switch (action) {
      case 'create':
        return this.createTemplate(params);
      case 'update':
        return this.updateTemplate(params);
      case 'delete':
        return this.deleteTemplate(params.templateId);
      case 'get':
        return this.getTemplate(params.templateId);
      case 'list':
        return this.listTemplates(params.category);
      case 'render':
        return this.renderTemplate(params.templateId, params.metadata);
      default:
        return this.getErrorOutputs('不支持的操作类型');
    }
  }

  private createTemplate(params: any): any {
    if (!params.templateName || !params.templateContent) {
      return this.getErrorOutputs('模板名称和内容不能为空');
    }

    const templateId = params.templateId || `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const template = {
      id: templateId,
      name: params.templateName,
      content: params.templateContent,
      type: params.templateType,
      category: params.category,
      variables: params.variables,
      metadata: params.metadata,
      isActive: params.isActive,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      templateId,
      template,
      templates: [],
      error: '',
      onCreated: true,
      onUpdated: false,
      onDeleted: false
    };
  }

  private updateTemplate(params: any): any {
    if (!params.templateId) {
      return this.getErrorOutputs('模板ID不能为空');
    }

    const template = {
      id: params.templateId,
      name: params.templateName,
      content: params.templateContent,
      type: params.templateType,
      category: params.category,
      variables: params.variables,
      metadata: params.metadata,
      isActive: params.isActive,
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      templateId: params.templateId,
      template,
      templates: [],
      error: '',
      onCreated: false,
      onUpdated: true,
      onDeleted: false
    };
  }

  private deleteTemplate(templateId: string): any {
    if (!templateId) {
      return this.getErrorOutputs('模板ID不能为空');
    }

    return {
      success: true,
      templateId,
      template: null,
      templates: [],
      error: '',
      onCreated: false,
      onUpdated: false,
      onDeleted: true
    };
  }

  private getTemplate(templateId: string): any {
    if (!templateId) {
      return this.getErrorOutputs('模板ID不能为空');
    }

    const template = {
      id: templateId,
      name: '示例模板',
      content: '欢迎 {{name}}！您的验证码是 {{code}}。',
      type: 'text',
      category: 'verification',
      variables: ['name', 'code'],
      metadata: {},
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      templateId,
      template,
      templates: [],
      error: '',
      onCreated: false,
      onUpdated: false,
      onDeleted: false
    };
  }

  private listTemplates(category?: string): any {
    const templates = [
      {
        id: 'template_welcome',
        name: '欢迎模板',
        content: '欢迎 {{name}} 加入我们！',
        type: 'text',
        category: 'welcome',
        variables: ['name'],
        isActive: true
      },
      {
        id: 'template_verification',
        name: '验证码模板',
        content: '您的验证码是 {{code}}，请在5分钟内使用。',
        type: 'text',
        category: 'verification',
        variables: ['code'],
        isActive: true
      }
    ];

    const filteredTemplates = category 
      ? templates.filter(t => t.category === category)
      : templates;

    return {
      success: true,
      templateId: '',
      template: null,
      templates: filteredTemplates,
      error: '',
      onCreated: false,
      onUpdated: false,
      onDeleted: false
    };
  }

  private renderTemplate(templateId: string, data: any): any {
    if (!templateId) {
      return this.getErrorOutputs('模板ID不能为空');
    }

    // 获取模板
    const template = this.getTemplate(templateId).template;
    if (!template) {
      return this.getErrorOutputs('模板不存在');
    }

    // 渲染模板
    let renderedContent = template.content;
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{{${key}}}`;
      renderedContent = renderedContent.replace(new RegExp(placeholder, 'g'), String(value));
    }

    return {
      success: true,
      templateId,
      template: {
        ...template,
        renderedContent
      },
      templates: [],
      error: '',
      onCreated: false,
      onUpdated: false,
      onDeleted: false
    };
  }
}
